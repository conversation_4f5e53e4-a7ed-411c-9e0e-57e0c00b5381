<!DOCTYPE html>
<html>
<head>
    <title>Test JavaScript načítání</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { border: 1px solid #ddd; padding: 15px; margin: 10px 0; }
        .ok { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Test načítání JavaScript souboru</h1>
    
    <div class="test">
        <h2>1. Přímé načtení JavaScript souboru</h2>
        <div id="js-test-result">Testuje se...</div>
    </div>
    
    <div class="test">
        <h2>2. Test CSS souboru</h2>
        <div id="css-test-result">Testuje se...</div>
    </div>
    
    <div class="test">
        <h2>3. Simulace PrestaShop prostředí</h2>
        <div id="prestashop-test">
            <p>Vytváříme falešný PrestaShop objekt...</p>
        </div>
    </div>

    <!-- Načteme CSS -->
    <link rel="stylesheet" href="views/css/front.css">
    
    <!-- Vytvoříme falešný PrestaShop objekt -->
    <script>
        // Simulujeme PrestaShop objekt
        window.prestashop = {
            on: function(event, callback) {
                console.log('PrestaShop.on called:', event);
                this._events = this._events || {};
                this._events[event] = this._events[event] || [];
                this._events[event].push(callback);
            },
            emit: function(event, data) {
                console.log('PrestaShop.emit called:', event, data);
                this._events = this._events || {};
                if (this._events[event]) {
                    this._events[event].forEach(callback => callback(data));
                }
            },
            _events: {}
        };
        
        console.log('Falešný PrestaShop objekt vytvořen');
    </script>
    
    <!-- Načteme náš JavaScript -->
    <script src="views/js/front.js"></script>
    
    <script>
        // Test výsledků
        setTimeout(function() {
            const jsResult = document.getElementById('js-test-result');
            const cssResult = document.getElementById('css-test-result');
            const prestashopTest = document.getElementById('prestashop-test');
            
            // Test JavaScript
            if (typeof PriceInquiryJS !== 'undefined') {
                jsResult.innerHTML = '<span class="ok">✓ PriceInquiryJS objekt byl úspěšně načten!</span>';
                jsResult.innerHTML += '<br>Dostupné metody: ' + Object.keys(PriceInquiryJS).join(', ');
            } else {
                jsResult.innerHTML = '<span class="error">✗ PriceInquiryJS objekt nebyl načten</span>';
            }
            
            // Test CSS
            const testElement = document.createElement('div');
            testElement.className = 'pi-btn';
            document.body.appendChild(testElement);
            const styles = window.getComputedStyle(testElement);
            
            if (styles.backgroundColor !== 'rgba(0, 0, 0, 0)' || styles.padding !== '0px') {
                cssResult.innerHTML = '<span class="ok">✓ CSS soubor byl načten</span>';
            } else {
                cssResult.innerHTML = '<span class="error">✗ CSS soubor nebyl načten</span>';
            }
            document.body.removeChild(testElement);
            
            // Test PrestaShop
            if (typeof prestashop !== 'undefined') {
                prestashopTest.innerHTML += '<p class="ok">✓ PrestaShop objekt existuje</p>';
                
                // Test event systému
                let eventReceived = false;
                prestashop.on('test', function() {
                    eventReceived = true;
                });
                prestashop.emit('test');
                
                if (eventReceived) {
                    prestashopTest.innerHTML += '<p class="ok">✓ Event systém funguje</p>';
                } else {
                    prestashopTest.innerHTML += '<p class="error">✗ Event systém nefunguje</p>';
                }
            } else {
                prestashopTest.innerHTML += '<p class="error">✗ PrestaShop objekt neexistuje</p>';
            }
            
        }, 1000);
        
        // Zachytíme všechny console.log zprávy
        const originalLog = console.log;
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('PriceInquiry')) {
                const logDiv = document.createElement('div');
                logDiv.innerHTML = '<strong>Log:</strong> ' + message;
                document.body.appendChild(logDiv);
            }
            originalLog.apply(console, args);
        };
    </script>
</body>
</html>
