<?php
/**
 * Export Filtered Data AJAX Endpoint
 * CIG Realizace - Phase 09
 * Exports filtered order data in various formats
 */

// Include required files
require_once '../includes/auth_check.php';
require_once '../classes/FilterManager.php';

// Check if user is authenticated
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    // Get current user
    $current_user = getCurrentUser();

    // Get parameters
    $format = $_GET['format'] ?? 'csv';
    $filters = [];

    // Parse filters from GET parameters
    if (isset($_GET['filters'])) {
        $filters = json_decode($_GET['filters'], true) ?? [];
    } else {
        // Build filters from individual parameters
        $filters = [
            'sales_rep' => $_GET['sales_rep'] ?? '',
            'preview_status' => $_GET['preview_status'] ?? '',
            'status' => $_GET['status'] ?? '',
            'date_from' => $_GET['date_from'] ?? '',
            'date_to' => $_GET['date_to'] ?? '',
            'search' => $_GET['search'] ?? '',
            'technology' => $_GET['technology'] ?? '',
            'inventory_status' => $_GET['inventory_status'] ?? '',
            'is_completed' => isset($_GET['is_completed']) ? ($_GET['is_completed'] === '1') : null,
            'overdue_only' => isset($_GET['overdue_only']) ? ($_GET['overdue_only'] === '1') : false
        ];

        // Remove empty filters
        $filters = array_filter($filters, function($value) {
            return $value !== '' && $value !== null;
        });
    }

    // Validate format
    $allowed_formats = ['csv', 'excel'];
    if (!in_array($format, $allowed_formats)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid export format']);
        exit;
    }

    // Initialize filter manager
    $filterManager = new FilterManager();

    // Export the data
    $result = $filterManager->exportFilteredData($filters, $format);

    if (!$result) {
        http_response_code(500);
        echo json_encode(['error' => 'Export failed - no data found']);
        exit;
    }

} catch (Exception $e) {
    error_log("Export filtered data error: " . $e->getMessage());

    // Only send JSON error if headers haven't been sent yet
    if (!headers_sent()) {
        http_response_code(500);
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Internal server error']);
    }
}
