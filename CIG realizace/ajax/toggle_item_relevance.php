<?php
/**
 * AJAX endpoint for toggling item relevance
 * CIG Realizace - Phase 05
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and history logger
require_once '../includes/auth_check.php';
require_once '../classes/HistoryLogger.php';

// Check if user is logged in and has permission
$current_user = getCurrentUser();
if (!$current_user || !in_array($current_user['role'], ['admin', 'obchodnik'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Nemáte oprávnění k této akci']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

// Get and validate input
$item_id = intval($_POST['item_id'] ?? 0);
$is_relevant = intval($_POST['is_relevant'] ?? 1);

if (!$item_id) {
    echo json_encode(['success' => false, 'message' => 'Neplatné ID položky']);
    exit;
}

// Ensure is_relevant is 0 or 1
$is_relevant = $is_relevant ? 1 : 0;

try {
    $pdo = getDbConnection();
    $pdo->beginTransaction();
    
    // Get current item data
    $stmt = $pdo->prepare("
        SELECT oi.*, o.order_code, o.id as order_id
        FROM order_items oi
        LEFT JOIN orders o ON oi.order_id = o.id
        WHERE oi.id = ?
    ");
    $stmt->execute([$item_id]);
    $item = $stmt->fetch();
    
    if (!$item) {
        throw new Exception('Položka nebyla nalezena');
    }
    
    $old_relevance = $item['is_relevant'];
    
    // Don't update if relevance is the same
    if ($old_relevance == $is_relevant) {
        echo json_encode([
            'success' => true,
            'message' => 'Relevance nebyla změněna',
            'data' => [
                'item_id' => $item_id,
                'is_relevant' => $is_relevant
            ]
        ]);
        exit;
    }
    
    // Update item relevance
    $stmt = $pdo->prepare("
        UPDATE order_items 
        SET is_relevant = ?, updated_at = ? 
        WHERE id = ?
    ");
    $stmt->execute([$is_relevant, date('Y-m-d H:i:s'), $item_id]);
    
    // Log the change using HistoryLogger
    HistoryLogger::logItemRelevanceChange(
        $item['order_id'],
        $current_user['id'],
        $item_id,
        $item['catalog_code'],
        $is_relevant
    );
    
    $pdo->commit();
    
    $message = $is_relevant ? 
        'Položka byla označena jako relevantní' : 
        'Položka byla označena jako irelevantní';
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => [
            'item_id' => $item_id,
            'old_relevance' => $old_relevance,
            'new_relevance' => $is_relevant,
            'catalog_code' => $item['catalog_code']
        ]
    ]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error toggling item relevance: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při změně relevance položky: ' . $e->getMessage()
    ]);
}
?>
