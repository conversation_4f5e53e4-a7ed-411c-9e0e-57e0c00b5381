<?php
/**
 * AJAX endpoint for updating order delivery date
 * CIG Realizace - Phase 05
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and history logger
require_once '../includes/auth_check.php';
require_once '../classes/HistoryLogger.php';

// Check if user is logged in and has permission
$current_user = getCurrentUser();
if (!$current_user || !in_array($current_user['role'], ['admin', 'obchodnik', 'realizator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Nemáte oprávnění k této akci']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

// Get and validate input
$order_id = intval($_POST['order_id'] ?? 0);
$delivery_date = $_POST['delivery_date'] ?? '';

if (!$order_id) {
    echo json_encode(['success' => false, 'message' => 'Neplatné ID objednávky']);
    exit;
}

// Validate delivery date
if (empty($delivery_date)) {
    echo json_encode(['success' => false, 'message' => 'Datum dodání je povinné']);
    exit;
}

// Validate date format
$date_obj = DateTime::createFromFormat('Y-m-d', $delivery_date);
if (!$date_obj || $date_obj->format('Y-m-d') !== $delivery_date) {
    echo json_encode(['success' => false, 'message' => 'Neplatný formát data']);
    exit;
}

// Check if date is not in the past
$today = new DateTime();
$delivery_date_obj = new DateTime($delivery_date);
if ($delivery_date_obj < $today) {
    echo json_encode(['success' => false, 'message' => 'Datum dodání nemůže být v minulosti']);
    exit;
}

try {
    $pdo = getDbConnection();
    $pdo->beginTransaction();
    
    // Get current order data
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Objednávka nebyla nalezena');
    }
    
    // Check if order is approved
    if ($order['preview_status'] !== 'approved') {
        throw new Exception('Lze měnit termín dodání pouze u schválených objednávek');
    }
    
    $old_delivery_date = $order['expected_delivery_date'];
    
    // Don't update if date is the same
    if ($old_delivery_date === $delivery_date) {
        echo json_encode([
            'success' => true,
            'message' => 'Termín dodání nebyl změněn',
            'data' => [
                'order_id' => $order_id,
                'delivery_date' => $delivery_date
            ]
        ]);
        exit;
    }
    
    // Update delivery date
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET expected_delivery_date = ?, updated_at = ? 
        WHERE id = ?
    ");
    $stmt->execute([$delivery_date, date('Y-m-d H:i:s'), $order_id]);
    
    // Log the change using HistoryLogger
    HistoryLogger::logDeliveryDateChange($order_id, $current_user['id'], $old_delivery_date, $delivery_date);
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Termín dodání byl úspěšně aktualizován',
        'data' => [
            'order_id' => $order_id,
            'old_delivery_date' => $old_delivery_date,
            'new_delivery_date' => $delivery_date,
            'order_code' => $order['order_code']
        ]
    ]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error updating delivery date: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při aktualizaci termínu dodání: ' . $e->getMessage()
    ]);
}
?>
