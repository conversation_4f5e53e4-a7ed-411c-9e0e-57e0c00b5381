/* Reports and Statistics Styles */

/* Report Cards */
.report-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.report-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.report-card .card-body {
    padding: 1.5rem;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: scale(1.05);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h4 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.chart-container canvas {
    max-height: 100%;
}

/* Activity Feed */
.activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-icon.status-change {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.activity-icon.delivery-date {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.activity-icon.technology {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.activity-icon.item-relevance {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}

/* User Activity Table */
.user-activity-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.user-activity-table .table {
    margin-bottom: 0;
}

.user-activity-table .table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.user-activity-table .table tbody tr {
    transition: background-color 0.2s ease;
}

.user-activity-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

.user-activity-table .table td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* Role Badges */
.role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.role-badge.admin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.role-badge.obchodnik {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.role-badge.grafik {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.role-badge.realizator {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

/* Export Buttons */
.export-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.export-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.export-btn.pdf {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.export-btn.excel {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.export-btn.csv {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
}

/* Filters */
.report-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-filters .form-label {
    font-weight: 600;
    color: #495057;
}

.report-filters .form-control,
.report-filters .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.report-filters .form-control:focus,
.report-filters .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Loading States */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card h4 {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .activity-item {
        padding: 0.75rem;
    }
    
    .activity-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.75rem;
    }
    
    .export-buttons {
        flex-direction: column;
    }
    
    .export-btn {
        justify-content: center;
    }
    
    .user-activity-table .table td,
    .user-activity-table .table th {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .report-filters {
        padding: 1rem;
    }
    
    .stats-card .card-body {
        padding: 1rem;
    }
    
    .stats-card h4 {
        font-size: 1.75rem;
    }
    
    .chart-container {
        height: 200px;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .export-buttons,
    .btn {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
    
    .card-header {
        background: #f8f9fa !important;
        color: #495057 !important;
    }
    
    .stats-card {
        background: #f8f9fa !important;
        color: #495057 !important;
    }
    
    .chart-container {
        height: auto !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
