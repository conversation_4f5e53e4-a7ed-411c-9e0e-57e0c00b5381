/* CIG Realizace - Responsive Design Optimizations */

/* Mobile-first approach */
@media (max-width: 768px) {
    /* Navigation improvements */
    .navbar-nav {
        flex-direction: column;
        width: 100%;
    }
    
    .navbar-nav .nav-item {
        width: 100%;
        text-align: center;
    }
    
    /* Table responsiveness */
    .order-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
    }
    
    .order-table th,
    .order-table td {
        min-width: 120px;
        padding: 8px 4px;
        font-size: 0.85em;
    }
    
    /* Filter panel mobile optimization */
    .filter-panel {
        position: fixed;
        top: 0;
        left: -100%;
        width: 85%;
        height: 100vh;
        background: white;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        transition: left 0.3s ease;
        z-index: 1050;
        overflow-y: auto;
        padding: 20px;
    }
    
    .filter-panel.active {
        left: 0;
    }
    
    .filter-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1040;
        display: none;
    }
    
    .filter-overlay.active {
        display: block;
    }
    
    /* Mobile filter toggle button */
    .mobile-filter-toggle {
        display: block;
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        z-index: 1000;
        font-size: 1.2em;
    }
    
    /* Calendar mobile optimization */
    .calendar-grid {
        font-size: 0.75em;
        overflow-x: auto;
    }
    
    .calendar-day {
        min-height: 80px;
        padding: 2px;
    }
    
    .order-block {
        min-height: 25px;
        font-size: 0.65em;
        padding: 2px 4px;
        margin: 1px 0;
        border-radius: 3px;
    }
    
    /* Dashboard stats mobile */
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    /* Order detail mobile */
    .order-detail-layout {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .order-items-table {
        font-size: 0.85em;
    }
    
    /* Form improvements */
    .form-group {
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 1em;
        min-height: 44px; /* Touch target size */
    }
    
    /* Search improvements */
    .search-container {
        width: 100%;
        margin-bottom: 15px;
    }
    
    .search-suggestions {
        position: absolute;
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
    }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .order-detail-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .calendar-grid {
        font-size: 0.9em;
    }
    
    .order-block {
        font-size: 0.8em;
        min-height: 30px;
    }
    
    .filter-panel {
        width: 300px;
    }
    
    /* Navigation adjustments */
    .navbar-nav .nav-link {
        padding: 8px 12px;
    }
}

/* Desktop enhancements */
@media (min-width: 1025px) {
    .dashboard-layout {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: 30px;
    }
    
    .order-detail-layout {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Enhanced hover effects */
    .order-row:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    /* Calendar enhancements */
    .calendar-day:hover {
        background-color: #f8f9fa;
    }
    
    .order-block:hover {
        transform: scale(1.02);
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }
}

/* Print styles */
@media print {
    .navbar,
    .filter-panel,
    .mobile-filter-toggle,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .container-fluid {
        padding: 0;
    }
    
    .order-table {
        font-size: 12px;
    }
    
    .order-table th,
    .order-table td {
        padding: 4px;
        border: 1px solid #000;
    }
    
    .page-break {
        page-break-before: always;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .order-block,
    .stat-card,
    .btn {
        border-width: 0.5px;
    }
}
