<?php
/**
 * Script pro přidání vlastní sekce do modulu Technologie
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

echo "<h1>Přidání vlastní sekce do modulu Technologie</h1>";

// Zkontroluj, zda modul existuje
$module = Module::getInstanceByName('technologie');
if (!$module) {
    echo "<p style='color: red;'>❌ Modul 'technologie' neexistuje nebo není nainstalován</p>";
    exit;
}

echo "<p style='color: green;'>✅ Modul Technologie nalezen</p>";

// Zkontroluj, zda CustomModulesTabManager existuje
$manager_path = dirname(__FILE__) . '/classes/CustomModulesTabManager.php';
if (!file_exists($manager_path)) {
    echo "<p style='color: red;'>❌ CustomModulesTabManager.php nenalezen v classes/</p>";
    echo "<p><strong>Akce:</strong> Soubor by měl být v: $manager_path</p>";
    exit;
}

echo "<p style='color: green;'>✅ CustomModulesTabManager.php nalezen</p>";

// Načti CustomModulesTabManager
require_once($manager_path);

// Zkontroluj aktuální stav tabu
$controller_class = 'AdminTechnologie';
$current_tab_id = Tab::getIdFromClassName($controller_class);
if (!$current_tab_id) {
    echo "<p style='color: red;'>❌ Tab '$controller_class' neexistuje</p>";
    echo "<p>Modul pravděpodobně nemá vytvořený tab v administraci.</p>";
} else {
    echo "<p style='color: green;'>✅ Tab '$controller_class' nalezen (ID: $current_tab_id)</p>";
    
    // Získej informace o aktuálním tabu
    $sql = 'SELECT t.*, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_tab = ' . (int)$current_tab_id;

    $tab_info = Db::getInstance()->getRow($sql);
    if ($tab_info) {
        echo "<p><strong>Aktuální umístění:</strong> Parent ID: {$tab_info['id_parent']}, Pozice: {$tab_info['position']}</p>";
        echo "<p><strong>Název:</strong> {$tab_info['name']}</p>";
    }
}

echo "<h2>Stav vlastní sekce</h2>";
if (CustomModulesTabManager::customModulesSectionExists()) {
    echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' existuje</p>";
    
    $custom_section_id = Tab::getIdFromClassName('AdminCustomModules');
    
    // Zkontroluj, zda je tab už v vlastní sekci
    if ($current_tab_id && $tab_info && $tab_info['id_parent'] == $custom_section_id) {
        echo "<p style='color: green;'>✅ Tab Technologie je už v sekci 'Vlastní moduly'</p>";
    } else if ($current_tab_id) {
        echo "<p style='color: orange;'>⚠️ Tab Technologie NENÍ v sekci 'Vlastní moduly'</p>";
        
        if (isset($_GET['action']) && $_GET['action'] == 'move') {
            echo "<h2>Přesun do vlastní sekce...</h2>";
            
            $result = CustomModulesTabManager::moveTabToCustomModules($controller_class);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Tab úspěšně přesunut do sekce 'Vlastní moduly'</p>";
            } else {
                echo "<p style='color: red;'>❌ Chyba při přesunu tabu</p>";
            }
        } else {
            echo "<p><a href='?action=move' style='background: #4caf50; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>🔄 Přesunout do vlastní sekce</a></p>";
        }
    }
    
    // Zobraz všechny moduly v sekci
    $sql = 'SELECT t.class_name, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_parent = ' . (int)$custom_section_id . ' AND t.active = 1 
            ORDER BY t.position';
    
    $modules_in_section = Db::getInstance()->executeS($sql);
    if ($modules_in_section) {
        echo "<h3>Moduly v sekci:</h3>";
        echo "<ul>";
        foreach ($modules_in_section as $mod) {
            echo "<li><strong>{$mod['name']}</strong> ({$mod['class_name']})</li>";
        }
        echo "</ul>";
    }
    
} else {
    echo "<p style='color: orange;'>⚠️ Sekce 'Vlastní moduly' neexistuje</p>";
    echo "<p><a href='?action=create_section' style='background: #2196f3; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>➕ Vytvořit sekci</a></p>";
    
    if (isset($_GET['action']) && $_GET['action'] == 'create_section') {
        echo "<h2>Vytváření sekce...</h2>";
        $result = CustomModulesTabManager::createOrGetCustomModulesTab();
        if ($result) {
            echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' vytvořena (ID: $result)</p>";
            echo "<p><a href='?'>🔄 Obnovit stránku</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Chyba při vytváření sekce</p>";
        }
    }
}

echo "<h2>Úprava kódu modulu</h2>";

// Zkontroluj, zda modul už používá CustomModulesTabManager
$module_file = dirname(__FILE__) . '/technologie.php';
if (file_exists($module_file)) {
    $module_content = file_get_contents($module_file);
    
    if (strpos($module_content, 'CustomModulesTabManager') !== false) {
        echo "<p style='color: green;'>✅ Modul už používá CustomModulesTabManager</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Modul nepoužívá CustomModulesTabManager</p>";
        echo "<p><strong>Doporučení:</strong> Upravte metody createTab() a removeTab() v souboru technologie.php</p>";
        
        if (isset($_GET['action']) && $_GET['action'] == 'update_code') {
            echo "<h2>Aktualizace kódu...</h2>";
            
            // Najdi a uprav createTab metodu
            if (strpos($module_content, 'private function createTab()') !== false) {
                // Nahraď existující createTab metodu
                $new_create_tab = "    private function createTab()\n    {\n        require_once(_PS_MODULE_DIR_ . \$this->name . '/classes/CustomModulesTabManager.php');\n        return CustomModulesTabManager::createModuleTab('AdminTechnologie', 'Technologie', \$this->name);\n    }";
                
                $pattern = '/private function createTab\(\)\s*\{[^}]*\}/s';
                $module_content = preg_replace($pattern, $new_create_tab, $module_content);
                
                echo "<p>✅ Metoda createTab() aktualizována</p>";
            }
            
            // Najdi a uprav removeTab metodu
            if (strpos($module_content, 'private function removeTab()') !== false) {
                $new_remove_tab = "    private function removeTab()\n    {\n        require_once(_PS_MODULE_DIR_ . \$this->name . '/classes/CustomModulesTabManager.php');\n        return CustomModulesTabManager::removeModuleTab('AdminTechnologie');\n    }";
                
                $pattern = '/private function removeTab\(\)\s*\{[^}]*\}/s';
                $module_content = preg_replace($pattern, $new_remove_tab, $module_content);
                
                echo "<p>✅ Metoda removeTab() aktualizována</p>";
            }
            
            // Ulož soubor
            if (file_put_contents($module_file, $module_content)) {
                echo "<p style='color: green;'>✅ Soubor technologie.php byl aktualizován</p>";
            } else {
                echo "<p style='color: red;'>❌ Chyba při ukládání souboru</p>";
            }
            
        } else {
            echo "<p><a href='?action=update_code' style='background: #ff9800; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>🔧 Aktualizovat kód automaticky</a></p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ Hlavní soubor modulu nenalezen: $module_file</p>";
}

echo "<hr>";
echo "<p><a href='../'>← Zpět na seznam modulů</a></p>";
echo "<p><small>Script: " . __FILE__ . "</small></p>";
?>
