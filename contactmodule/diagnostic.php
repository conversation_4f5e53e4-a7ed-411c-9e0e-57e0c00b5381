<?php
/**
 * Diagnostický skript pro Contact Module
 * Uložte do root složky PrestaShop a spusťte v prohlížeči
 */

require_once(dirname(__FILE__).'/config/config.inc.php');

echo "<h2>Diagnostika Contact Module</h2>";

// Test 1: Existence modulu
$module = Module::getInstanceByName('contactmodule');
echo "<h3>Test 1: Existence modulu</h3>";
if ($module) {
    echo "<p>✓ Modul nalezen (ID: {$module->id})</p>";
    echo "<p>✓ Aktivní: " . ($module->active ? 'ANO' : 'NE') . "</p>";
} else {
    echo "<p style='color: red;'>✗ Modul nenalezen!</p>";
    exit;
}

// Test 2: Registrované hooks
echo "<h3>Test 2: Registrované hooks</h3>";
$sql = 'SELECT h.name 
        FROM '._DB_PREFIX_.'hook_module hm 
        LEFT JOIN '._DB_PREFIX_.'hook h ON (h.id_hook = hm.id_hook) 
        WHERE hm.id_module = '.(int)$module->id.' 
        ORDER BY hm.position';

$hooks = Db::getInstance()->executeS($sql);
if ($hooks) {
    echo "<ul>";
    foreach ($hooks as $hook) {
        $color = ($hook['name'] == 'actionOutputHTMLBefore') ? 'color: red;' : 'color: green;';
        echo "<li style='$color'>{$hook['name']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>✗ Žádné hooks registrovány!</p>";
}

// Test 3: Konfigurace
echo "<h3>Test 3: Konfigurace modulu</h3>";
$configs = [
    'CONTACT_COMPANY_NAME',
    'CONTACT_ADDRESS', 
    'CONTACT_PHONE',
    'CONTACT_EMAIL',
    'CONTACT_MAP_EMBED',
    'CONTACT_REPRESENTATIVES'
];

foreach ($configs as $config) {
    $value = Configuration::get($config);
    $status = !empty($value) ? '✓' : '✗';
    $length = is_string($value) ? strlen($value) : 0;
    echo "<p>$status $config: " . ($length > 0 ? "Uloženo ($length znaků)" : "Prázdné") . "</p>";
}

// Test 4: Existence souborů
echo "<h3>Test 4: Existence souborů</h3>";
$files = [
    'modules/contactmodule/contactmodule.php',
    'modules/contactmodule/ajax.php',
    'modules/contactmodule/views/css/contactmodule.css',
    'modules/contactmodule/views/templates/front/contact.tpl'
];

foreach ($files as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✓' : '✗';
    echo "<p>$status $file</p>";
}

// Test 5: Test metod modulu
echo "<h3>Test 5: Test metod modulu</h3>";
try {
    $content = $module->getContactPageContent();
    echo "<p>✓ getContactPageContent(): " . (strlen($content) > 0 ? "Funguje (" . strlen($content) . " znaků)" : "Prázdný obsah") . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ getContactPageContent(): " . $e->getMessage() . "</p>";
}

try {
    $content = $module->getContactPageContentWithInlineStyles();
    echo "<p>✓ getContactPageContentWithInlineStyles(): " . (strlen($content) > 0 ? "Funguje (" . strlen($content) . " znaků)" : "Prázdný obsah") . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ getContactPageContentWithInlineStyles(): " . $e->getMessage() . "</p>";
}

// Test 6: Test AJAX
echo "<h3>Test 6: Test AJAX endpointu</h3>";
$ajax_file = 'modules/contactmodule/ajax.php';
if (file_exists($ajax_file)) {
    echo "<p>✓ Ajax soubor existuje</p>";
    
    // Test načtení
    ob_start();
    try {
        include $ajax_file;
        $ajax_content = ob_get_clean();
        echo "<p>✓ Ajax soubor se načetl (" . strlen($ajax_content) . " znaků)</p>";
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>✗ Ajax chyba: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Ajax soubor neexistuje</p>";
}

// Test 7: CMS stránka
echo "<h3>Test 7: CMS stránka</h3>";
try {
    $cms = new CMS(22);
    if (Validate::isLoadedObject($cms)) {
        $content = $cms->content[1]; // Pro český jazyk
        echo "<p>✓ CMS stránka načtena</p>";
        echo "<p>Obsah obsahuje [contactmodule]: " . (strpos($content, '[contactmodule]') !== false ? '✓ ANO' : '✗ NE') . "</p>";
        echo "<p>Délka obsahu: " . strlen($content) . " znaků</p>";
    } else {
        echo "<p style='color: red;'>✗ CMS stránka s ID 22 nenalezena</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ CMS chyba: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Doporučení:</h3>";

// Kontrola problémů a doporučení
$issues = [];
if (!$module->active) $issues[] = "Modul není aktivní";
if (empty($hooks)) $issues[] = "Žádné hooks nejsou registrovány";

$has_problem_hook = false;
if ($hooks) {
    foreach ($hooks as $hook) {
        if ($hook['name'] == 'actionOutputHTMLBefore') {
            $has_problem_hook = true;
            break;
        }
    }
}
if ($has_problem_hook) $issues[] = "Problematický hook 'actionOutputHTMLBefore' je stále registrován";

if (empty($issues)) {
    echo "<p style='color: green;'>✓ Vše vypadá v pořádku! Zkuste vyčistit cache.</p>";
} else {
    echo "<div style='background: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>";
    echo "<strong>Nalezené problémy:</strong><ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul></div>";
}

echo "<p><strong>Další kroky:</strong></p>";
echo "<ol>";
echo "<li>Vyčistěte cache PrestaShop</li>";
echo "<li>Pokud problémy přetrvávají, zkuste přeinstalovat modul</li>";
echo "<li>Nebo použijte ruční řešení níže</li>";
echo "</ol>";
?>