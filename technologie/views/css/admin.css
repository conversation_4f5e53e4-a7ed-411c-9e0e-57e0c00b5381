/**
 * CSS styly pro admin rozhraní modulu Technologie
 * Kompatibilní s PrestaShop 8.2.0 admin témou
 */

/* === OBECNÉ STYLY === */
.technologie-admin {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

/* === SEZNAM TECHNOLOGIÍ === */
.technologie-thumb {
    max-width: 50px;
    max-height: 50px;
    object-fit: cover;
    border-radius: 4px;
    transition: transform 0.2s ease;
}

.technologie-thumb:hover {
    transform: scale(1.1);
    cursor: pointer;
}

.technologie-description {
    max-width: none;
    line-height: 1.4;
    cursor: help;
}

.position-badge {
    min-width: 30px;
    display: inline-block;
}

.drag-handle {
    cursor: move;
    color: #6c757d;
    margin-left: 8px;
    transition: color 0.2s ease;
}

.drag-handle:hover {
    color: #007cff;
}

/* === DRAG & DROP === */
.sortable-placeholder {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    height: 60px;
    margin: 2px 0;
}

.ui-sortable-helper {
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}

.technologie-row.ui-sortable-helper {
    display: table;
    width: 100%;
}

/* === FORMULÁŘ === */
.current-tech-image {
    max-width: 200px;
    max-height: 200px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.image-preview {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.image-preview img {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group .help-block {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

.form-group .required::after {
    content: " *";
    color: #dc3545;
}

/* === TLAČÍTKA === */
.btn-technologie {
    background-color: #007cff;
    border-color: #007cff;
    color: white;
    transition: all 0.2s ease;
}

.btn-technologie:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
}

/* === HROMADNÉ AKCE === */
.bulk-actions {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-top: 15px;
}

.bulk-actions .form-group {
    margin-bottom: 0;
}

/* === RESPONZIVNÍ DESIGN === */
@media (max-width: 768px) {
    .technologie-thumb {
        max-width: 40px;
        max-height: 40px;
    }
    
    .technologie-description {
        max-width: 150px;
    }
    
    .current-tech-image {
        max-width: 150px;
        max-height: 150px;
    }
    
    .btn-group .btn {
        padding: 4px 8px;
        font-size: 12px;
    }
}

/* === ANIMACE === */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.technologie-row {
    animation: fadeIn 0.3s ease;
}

/* === TOOLTIP STYLY === */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    max-width: 300px;
    text-align: left;
}

/* === LOADING STAVY === */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === CHYBOVÉ STAVY === */
.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
    margin: 10px 0;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #c3e6cb;
    margin: 10px 0;
}

/* === PŘIZPŮSOBENÍ PRESTASHOP ADMIN STYLU === */
.panel .panel-heading {
    background: linear-gradient(#ffffff, #eaeaea);
    color: white;
    border-radius: 8px 8px 0 0;
}

.panel .panel-heading i {
    margin-right: 8px;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* === DRAG & DROP STYLY === */
.drag-handle {
    cursor: move;
    color: #666;
    opacity: 0.7;
    transition: all 0.2s ease;
    padding: 2px;
}

.drag-handle:hover {
    opacity: 1;
    color: #007cff;
    transform: scale(1.1);
}

.sortable-ghost {
    opacity: 0.4;
    background: #f8f9fa;
}

.sortable-chosen {
    background: #e3f2fd;
}

.sortable-drag {
    background: #007cff;
    color: white;
    box-shadow: 0 5px 15px rgba(0, 124, 255, 0.3);
}

.dragging {
    opacity: 0.8;
    transform: rotate(2deg);
}

#sortable-technologie tr {
    transition: all 0.2s ease;
}

#sortable-technologie tr:hover {
    background-color: #f8f9fa;
}

/* === CUSTOM CHECKBOX STYLY === */
.row-selector {
    transform: scale(1.2);
    margin: 0;
    vertical-align: middle;
}

#select-all {
    transform: scale(1.2);
    vertical-align: middle;
}

/* === ZAROVNÁNÍ PRVNÍHO SLOUPCE === */
.table td:first-child,
.table th:first-child {
    text-align: center;
    vertical-align: middle;
    padding: 8px 4px;
}

.checkbox-drag-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* === BADGE STYLY === */
.badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
}

.badge-success {
    background-color: #28a745;
}

.badge-danger {
    background-color: #dc3545;
}

.badge-info {
    background-color: #17a2b8;
}

/* === STATUS TOGGLE === */
.status-toggle {
    transition: all 0.2s ease;
    user-select: none;
}

.status-toggle:hover {
    opacity: 0.8;
    transform: scale(1.05);
}

.status-toggle:active {
    transform: scale(0.95);
}
