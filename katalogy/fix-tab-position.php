<?php
/**
 * Rychlá oprava pozice sekce "Vlastní moduly"
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

echo "<h1>Oprava pozice sekce 'Vlastní moduly'</h1>";

// Načti CustomModulesTabManager
require_once(dirname(__FILE__) . '/classes/CustomModulesTabManager.php');

// Zkontroluj, zda sekce existuje
if (!CustomModulesTabManager::customModulesSectionExists()) {
    echo "<p style='color: red;'>❌ Sekce 'Vlastní moduly' neexistuje</p>";
    echo "<p><a href='manage-custom-tabs.php'>← Zpět na správu tabů</a></p>";
    exit;
}

// Získej aktuální pozice
$sql = 'SELECT t.id_tab, t.class_name, t.position, tl.name 
        FROM `' . _DB_PREFIX_ . 'tab` t
        LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
        WHERE t.id_parent = 0 AND t.active = 1 
        AND (t.class_name = "AdminParentPreferences" OR t.class_name = "AdminCustomModules")
        ORDER BY t.position';

$tabs = Db::getInstance()->executeS($sql);

$config_position = null;
$custom_position = null;
$config_id = null;
$custom_id = null;

foreach ($tabs as $tab) {
    if ($tab['class_name'] == 'AdminParentPreferences') {
        $config_position = $tab['position'];
        $config_id = $tab['id_tab'];
    }
    if ($tab['class_name'] == 'AdminCustomModules') {
        $custom_position = $tab['position'];
        $custom_id = $tab['id_tab'];
    }
}

echo "<h2>Aktuální stav:</h2>";
if ($config_position !== null) {
    echo "<p><strong>KONFIGURACE:</strong> pozice $config_position (ID: $config_id)</p>";
} else {
    echo "<p style='color: red;'>❌ KONFIGURACE nenalezena</p>";
}

if ($custom_position !== null) {
    echo "<p><strong>VLASTNÍ MODULY:</strong> pozice $custom_position (ID: $custom_id)</p>";
} else {
    echo "<p style='color: red;'>❌ VLASTNÍ MODULY nenalezeny</p>";
}

// Zkontroluj, zda je potřeba oprava
if ($config_position !== null && $custom_position !== null) {
    $target_position = $config_position + 1;
    
    if ($custom_position == $target_position) {
        echo "<p style='color: green;'>✅ Pozice je správná - Vlastní moduly jsou hned za KONFIGURACE</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Pozice není správná - Vlastní moduly by měly být na pozici $target_position</p>";
        
        if (isset($_GET['action']) && $_GET['action'] == 'fix') {
            echo "<h2>Provádím opravu...</h2>";
            
            // Krok 1: Přesuň všechny taby s pozicí >= target_position o 1 doprava (kromě našeho)
            $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                    SET position = position + 1 
                    WHERE position >= ' . (int)$target_position . ' AND id_parent = 0 
                    AND class_name != "AdminCustomModules"';
            
            if (Db::getInstance()->execute($sql)) {
                $affected = Db::getInstance()->Affected_Rows();
                echo "<p>✅ Posunuto $affected tabů doprava</p>";
            } else {
                echo "<p style='color: red;'>❌ Chyba při posunu tabů</p>";
            }
            
            // Krok 2: Nastav správnou pozici pro Vlastní moduly
            $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                    SET position = ' . (int)$target_position . ' 
                    WHERE class_name = "AdminCustomModules"';
            
            if (Db::getInstance()->execute($sql)) {
                echo "<p style='color: green;'>✅ Vlastní moduly přesunuty na pozici $target_position</p>";
                echo "<p><strong>Oprava dokončena!</strong> Obnovte administraci PrestaShop.</p>";
            } else {
                echo "<p style='color: red;'>❌ Chyba při nastavení pozice Vlastních modulů</p>";
            }
            
        } else {
            echo "<p><a href='?action=fix' style='background: #f44336; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🔧 OPRAVIT POZICI</a></p>";
        }
    }
}

echo "<hr>";
echo "<p><a href='debug-tab-positions.php'>🔍 Debug pozic</a> | <a href='manage-custom-tabs.php'>🔧 Správa tabů</a></p>";
echo "<p><small>Script: " . __FILE__ . "</small></p>";
?>
