<?php
require_once '../includes/session.php';
require_once '../includes/auth_check.php';
require_once '../config/database.php';

header('Content-Type: application/json');

// Check admin access
if ($current_user['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Nemáte oprávnění']);
    exit;
}

// Get parameters
$period = $_GET['period'] ?? '30'; // days
$type = $_GET['type'] ?? 'overview'; // overview, activity, orders, users

try {
    $pdo = getDbConnection();
    $stats = [];
    
    switch ($type) {
        case 'overview':
            $stats = getOverviewStats($pdo, $period);
            break;
            
        case 'activity':
            $stats = getActivityStats($pdo, $period);
            break;
            
        case 'orders':
            $stats = getOrderStats($pdo, $period);
            break;
            
        case 'users':
            $stats = getUserStats($pdo, $period);
            break;
            
        default:
            throw new Exception('Neplatný typ statistik');
    }
    
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'period' => $period,
        'type' => $type,
        'generated_at' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Error getting system stats: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při načítání statistik',
        'error' => $e->getMessage()
    ]);
}

function getOverviewStats($pdo, $period) {
    $date_from = date('Y-m-d', strtotime("-{$period} days"));
    
    // Basic counts
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM orders");
    $total_orders = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
    $total_users = $stmt->fetch()['total'];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM order_history WHERE created_at >= ?");
    $stmt->execute([$date_from]);
    $recent_activity = $stmt->fetch()['total'];
    
    // Order status distribution
    $stmt = $pdo->query("
        SELECT preview_status, COUNT(*) as count 
        FROM orders 
        GROUP BY preview_status
    ");
    $status_distribution = [];
    while ($row = $stmt->fetch()) {
        $status_distribution[$row['preview_status']] = $row['count'];
    }
    
    // Sales rep distribution
    $stmt = $pdo->query("
        SELECT sales_rep_name, COUNT(*) as count 
        FROM orders 
        GROUP BY sales_rep_name 
        ORDER BY count DESC 
        LIMIT 10
    ");
    $sales_rep_distribution = $stmt->fetchAll();
    
    return [
        'totals' => [
            'orders' => $total_orders,
            'users' => $total_users,
            'recent_activity' => $recent_activity
        ],
        'status_distribution' => $status_distribution,
        'sales_rep_distribution' => $sales_rep_distribution
    ];
}

function getActivityStats($pdo, $period) {
    $date_from = date('Y-m-d', strtotime("-{$period} days"));
    
    // Daily activity
    $stmt = $pdo->prepare("
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM order_history 
        WHERE created_at >= ?
        GROUP BY DATE(created_at)
        ORDER BY date
    ");
    $stmt->execute([$date_from]);
    $daily_activity = $stmt->fetchAll();
    
    // Activity by type
    $stmt = $pdo->prepare("
        SELECT action_type, COUNT(*) as count
        FROM order_history 
        WHERE created_at >= ?
        GROUP BY action_type
        ORDER BY count DESC
    ");
    $stmt->execute([$date_from]);
    $activity_by_type = $stmt->fetchAll();
    
    // Activity by user
    $stmt = $pdo->prepare("
        SELECT u.username, u.full_name, COUNT(oh.id) as count
        FROM order_history oh
        LEFT JOIN users u ON oh.user_id = u.id
        WHERE oh.created_at >= ?
        GROUP BY oh.user_id
        ORDER BY count DESC
        LIMIT 10
    ");
    $stmt->execute([$date_from]);
    $activity_by_user = $stmt->fetchAll();
    
    // Hourly distribution
    $stmt = $pdo->prepare("
        SELECT HOUR(created_at) as hour, COUNT(*) as count
        FROM order_history 
        WHERE created_at >= ?
        GROUP BY HOUR(created_at)
        ORDER BY hour
    ");
    $stmt->execute([$date_from]);
    $hourly_distribution = $stmt->fetchAll();
    
    return [
        'daily_activity' => $daily_activity,
        'activity_by_type' => $activity_by_type,
        'activity_by_user' => $activity_by_user,
        'hourly_distribution' => $hourly_distribution
    ];
}

function getOrderStats($pdo, $period) {
    $date_from = date('Y-m-d', strtotime("-{$period} days"));
    
    // Orders created in period
    $stmt = $pdo->prepare("
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM orders 
        WHERE created_at >= ?
        GROUP BY DATE(created_at)
        ORDER BY date
    ");
    $stmt->execute([$date_from]);
    $orders_created = $stmt->fetchAll();
    
    // Orders by status
    $stmt = $pdo->query("
        SELECT preview_status, status, COUNT(*) as count
        FROM orders 
        GROUP BY preview_status, status
    ");
    $orders_by_status = $stmt->fetchAll();
    
    // Average processing time
    $stmt = $pdo->query("
        SELECT 
            AVG(DATEDIFF(preview_approved_date, created_at)) as avg_approval_days,
            AVG(DATEDIFF(expected_delivery_date, preview_approved_date)) as avg_delivery_days
        FROM orders 
        WHERE preview_approved_date IS NOT NULL
    ");
    $processing_times = $stmt->fetch();
    
    // Orders by month
    $stmt = $pdo->query("
        SELECT 
            YEAR(created_at) as year,
            MONTH(created_at) as month,
            COUNT(*) as count
        FROM orders 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY YEAR(created_at), MONTH(created_at)
        ORDER BY year, month
    ");
    $monthly_orders = $stmt->fetchAll();
    
    return [
        'orders_created' => $orders_created,
        'orders_by_status' => $orders_by_status,
        'processing_times' => $processing_times,
        'monthly_orders' => $monthly_orders
    ];
}

function getUserStats($pdo, $period) {
    $date_from = date('Y-m-d', strtotime("-{$period} days"));
    
    // User activity summary
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.full_name, u.role, u.created_at,
               COUNT(oh.id) as total_actions,
               MAX(oh.created_at) as last_activity,
               COUNT(DISTINCT DATE(oh.created_at)) as active_days
        FROM users u
        LEFT JOIN order_history oh ON u.id = oh.user_id AND oh.created_at >= ?
        WHERE u.is_active = 1
        GROUP BY u.id
        ORDER BY total_actions DESC
    ");
    $stmt->execute([$date_from]);
    $user_activity = $stmt->fetchAll();
    
    // Users by role
    $stmt = $pdo->query("
        SELECT role, COUNT(*) as count
        FROM users 
        WHERE is_active = 1
        GROUP BY role
    ");
    $users_by_role = $stmt->fetchAll();
    
    // Login statistics (if we had login tracking)
    // For now, we'll use order_history as proxy for activity
    $stmt = $pdo->prepare("
        SELECT DATE(oh.created_at) as date, COUNT(DISTINCT oh.user_id) as active_users
        FROM order_history oh
        WHERE oh.created_at >= ?
        GROUP BY DATE(oh.created_at)
        ORDER BY date
    ");
    $stmt->execute([$date_from]);
    $daily_active_users = $stmt->fetchAll();
    
    return [
        'user_activity' => $user_activity,
        'users_by_role' => $users_by_role,
        'daily_active_users' => $daily_active_users
    ];
}
?>
