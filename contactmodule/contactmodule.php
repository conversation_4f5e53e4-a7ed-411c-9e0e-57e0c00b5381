<?php
/**
 * Contact Module for PrestaShop 8.2.0
 * Simple version for easier installation
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class ContactModule extends Module
{
    public function __construct()
    {
        $this->name = 'contactmodule';
        $this->tab = 'front_office_features';
        $this->version = '1.0.0';
        $this->author = '<PERSON><PERSON><PERSON>';
        $this->need_instance = 0;
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = 'Contact Page Module';
        $this->description = 'Module pro vytvoření kontaktní stránky';
        $this->confirmUninstall = 'Opravdu chcete odinstalovat tento modul?';
    }

    public function install()
    {
        return parent::install() &&
               $this->registerHook('actionFrontControllerSetMedia') &&
               $this->registerHook('displayHeader') &&
               $this->registerHook('filterCmsContent') &&
               $this->registerHook('actionOutputHTMLBefore') &&
               $this->createDefaultConfig();
    }

    public function uninstall()
    {
        return $this->deleteConfig() && parent::uninstall();
    }

    private function createDefaultConfig()
    {
        Configuration::updateValue('CONTACT_COMPANY_NAME', 'CZECH IMAGE GROUP s.r.o.');
        Configuration::updateValue('CONTACT_ADDRESS', 'Sladovnická 508/19<br>620 00 Brno<br>Česká republika');
        Configuration::updateValue('CONTACT_PHONE', '+*********** 780');
        Configuration::updateValue('CONTACT_EMAIL', '<EMAIL>');
        Configuration::updateValue('CONTACT_MAP_EMBED', '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2607.*********!2d16.608!3d49.195!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDnCsDExJzQyLjAiTiAxNsKwMzYnMjguOCJF!5e0!3m2!1scs!2scz!4v*********0" width="100%" height="300" style="border:0;" allowfullscreen="" loading="lazy"></iframe>');
        Configuration::updateValue('CONTACT_BANK_INFO', 'Účet: *********/0100<br>IBAN: CZ*********0*********');

        $representatives = [
            [
                'name' => 'Jiří Belajev, DiS.',
                'position' => 'Obchodní oddělení',
                'phone' => '+*********** 616',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ],
            [
                'name' => 'Daniela Pavlová',
                'position' => 'Obchodní oddělení',
                'phone' => '+420 778 086 292',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ],
            [
                'name' => 'Vladimír Peška',
                'position' => 'Obchodní oddělení',
                'phone' => '+420 778 086 072',
                'email' => '<EMAIL>',
                'region' => 'Prodej'
            ],
            [
                'name' => 'Mirka Horáčková',
                'position' => 'Obchodní oddělení',
                'phone' => '+*********** 515',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ],
            [
                'name' => 'Nikol Krásová',
                'position' => 'Obchodní oddělení',
                'phone' => '+*********** 531',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ],
            [
                'name' => 'Sylvie Swierczková',
                'position' => 'Obchodní oddělení',
                'phone' => '+420 774 583 519',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ]
        ];

        Configuration::updateValue('CONTACT_REPRESENTATIVES', json_encode($representatives));

        $this->updateDefaultOffices();
        return true;
    }

    private function updateDefaultRepresentatives()
    {
        $representatives = [
            [
                'name' => 'Jiří Belajev, DiS.',
                'position' => 'Obchodní oddělení',
                'phone' => '+*********** 616',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ],
            [
                'name' => 'Daniela Pavlová',
                'position' => 'Obchodní oddělení',
                'phone' => '+420 778 086 292',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ],
            [
                'name' => 'Vladimír Peška',
                'position' => 'Obchodní oddělení',
                'phone' => '+420 778 086 072',
                'email' => '<EMAIL>',
                'region' => 'Prodej'
            ],
            [
                'name' => 'Mirka Horáčková',
                'position' => 'Obchodní oddělení',
                'phone' => '+*********** 515',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ],
            [
                'name' => 'Nikol Krásová',
                'position' => 'Obchodní oddělení',
                'phone' => '+*********** 531',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ],
            [
                'name' => 'Sylvie Swierczková',
                'position' => 'Obchodní oddělení',
                'phone' => '+420 774 583 519',
                'email' => '<EMAIL>',
                'region' => 'Obchodní zástupce'
            ]
        ];

        Configuration::updateValue('CONTACT_REPRESENTATIVES', json_encode($representatives));
        return true;
    }

    private function updateDefaultOffices()
    {
        $offices = [
            [
                'name' => 'Kancelář Brno',
                'address' => 'Ledečská 3028<br>Havlíčkův Brod, 580 01',
                'email' => '<EMAIL>',
                'contact_person' => 'Vladimír Peška',
                'phone' => '+420 778 086 072',
                'bank_info' => 'UniCredit Bank<br>CZK č.ú. **********/2700<br>SWIFT: BACXCZPP<br><br>UniCredit Bank Slovakia a. s.<br>EUR č.ú.: **********/1111',
                'type' => 'office'
            ],
            [
                'name' => 'Kancelář Praha',
                'address' => 'Chlumecká 1539<br>Praha 14 - Kyje, 198 00',
                'email' => '<EMAIL>',
                'contact_person' => 'Mirka Horáčková',
                'phone' => '+*********** 515',
                'bank_info' => '',
                'type' => 'office'
            ]
        ];

        Configuration::updateValue('CONTACT_OFFICES', json_encode($offices));
        return true;
    }

    private function deleteConfig()
    {
        Configuration::deleteByName('CONTACT_COMPANY_NAME');
        Configuration::deleteByName('CONTACT_ADDRESS');
        Configuration::deleteByName('CONTACT_PHONE');
        Configuration::deleteByName('CONTACT_EMAIL');
        Configuration::deleteByName('CONTACT_MAP_EMBED');
        Configuration::deleteByName('CONTACT_BANK_INFO');
        Configuration::deleteByName('CONTACT_REPRESENTATIVES');
        Configuration::deleteByName('CONTACT_OFFICES');
        return true;
    }

    public function hookActionFrontControllerSetMedia()
    {
        $this->context->controller->addCSS($this->_path . 'views/css/contactmodule.css');
    }

    public function getContent()
    {
        $output = '';

        if (Tools::isSubmit('submitContactModule')) {
            // Získání mapy bez HTML filtrování
            $map_embed_value = isset($_POST['map_embed']) ? $_POST['map_embed'] : '';

            // Detailní debug
            $debug_details = [
                'map_embed_value' => $map_embed_value,
                'map_embed_length' => strlen($map_embed_value),
                'map_embed_type' => gettype($map_embed_value),
                'is_empty' => empty($map_embed_value),
                'tools_value' => Tools::getValue('map_embed'),
                'tools_length' => strlen(Tools::getValue('map_embed'))
            ];

            Configuration::updateValue('CONTACT_COMPANY_NAME', Tools::getValue('company_name'));
            Configuration::updateValue('CONTACT_ADDRESS', Tools::getValue('address'));
            Configuration::updateValue('CONTACT_PHONE', Tools::getValue('phone'));
            Configuration::updateValue('CONTACT_EMAIL', Tools::getValue('email'));

            // Speciální zpracování pro mapu - base64 kódování pro bezpečnost
            if (!empty($map_embed_value) && strpos($map_embed_value, '<iframe') !== false) {
                // Uložíme jako base64 pro obejití HTML filtrů
                $encoded_map = base64_encode($map_embed_value);
                $result = Configuration::updateValue('CONTACT_MAP_EMBED_ENCODED', $encoded_map);
                $result2 = Configuration::updateValue('CONTACT_MAP_EMBED', $map_embed_value);
            } else {
                // Normální uložení pro jiný obsah
                $result = Configuration::updateValue('CONTACT_MAP_EMBED', $map_embed_value);
                $result2 = true;
            }

            if (!$result) {
                $output .= $this->displayError('Chyba při ukládání mapy do databáze.');
            }

            Configuration::updateValue('CONTACT_BANK_INFO', Tools::getValue('bank_info'));

            // Uložení obchodních zástupců
            $representatives = [];
            $rep_names = Tools::getValue('rep_name');
            $rep_positions = Tools::getValue('rep_position');
            $rep_phones = Tools::getValue('rep_phone');
            $rep_emails = Tools::getValue('rep_email');
            $rep_regions = Tools::getValue('rep_region');

            if (is_array($rep_names)) {
                for ($i = 0; $i < count($rep_names); $i++) {
                    if (!empty($rep_names[$i])) {
                        $representatives[] = [
                            'name' => $rep_names[$i],
                            'position' => isset($rep_positions[$i]) ? $rep_positions[$i] : '',
                            'phone' => isset($rep_phones[$i]) ? $rep_phones[$i] : '',
                            'email' => isset($rep_emails[$i]) ? $rep_emails[$i] : '',
                            'region' => isset($rep_regions[$i]) ? $rep_regions[$i] : ''
                        ];
                    }
                }
            }

            Configuration::updateValue('CONTACT_REPRESENTATIVES', json_encode($representatives));

            // Uložení kanceláří
            $offices = [];
            $office_names = Tools::getValue('office_name');
            $office_addresses = Tools::getValue('office_address');
            $office_emails = Tools::getValue('office_email');
            $office_contact_persons = Tools::getValue('office_contact_person');
            $office_phones = Tools::getValue('office_phone');
            $office_bank_infos = Tools::getValue('office_bank_info');

            if (is_array($office_names)) {
                for ($i = 0; $i < count($office_names); $i++) {
                    if (!empty($office_names[$i])) {
                        $offices[] = [
                            'name' => $office_names[$i],
                            'address' => isset($office_addresses[$i]) ? $office_addresses[$i] : '',
                            'email' => isset($office_emails[$i]) ? $office_emails[$i] : '',
                            'contact_person' => isset($office_contact_persons[$i]) ? $office_contact_persons[$i] : '',
                            'phone' => isset($office_phones[$i]) ? $office_phones[$i] : '',
                            'bank_info' => isset($office_bank_infos[$i]) ? $office_bank_infos[$i] : '',
                            'type' => 'office'
                        ];
                    }
                }
            }

            Configuration::updateValue('CONTACT_OFFICES', json_encode($offices));

            // Debug informace v konfirmaci
            $saved_map = Configuration::get('CONTACT_MAP_EMBED');
            $debug_info = 'Mapa: ' . (!empty($saved_map) ? 'Uložena (' . strlen($saved_map) . ' znaků)' : 'Prázdná');
            $debug_info .= ' | POST: ' . $debug_details['map_embed_length'] . ' znaků';
            $debug_info .= ' | Tools: ' . $debug_details['tools_length'] . ' znaků';

            $output .= $this->displayConfirmation('Nastavení bylo uloženo. ' . $debug_info);
        }

        if (Tools::isSubmit('updateCmsPage')) {
            $result = $this->updateCmsPageContent();
            if ($result) {
                $output .= $this->displayConfirmation('CMS stránka byla úspěšně aktualizována.');
            } else {
                $output .= $this->displayError('Chyba při aktualizaci CMS stránky.');
            }
        }

        if (Tools::isSubmit('insertShortcode')) {
            $result = $this->insertShortcodeIntoCmsPage();
            if ($result) {
                $output .= $this->displayConfirmation('Shortcode byl úspěšně vložen do CMS stránky.');
            } else {
                $output .= $this->displayError('Chyba při vkládání shortcode do CMS stránky.');
            }
        }

        if (Tools::isSubmit('resetToDefaults')) {
            $result = $this->createDefaultConfig();
            if ($result) {
                $output .= $this->displayConfirmation('Konfigurace byla obnovena na výchozí hodnoty.');
            } else {
                $output .= $this->displayError('Chyba při obnovování výchozích hodnot.');
            }
        }

        if (Tools::isSubmit('updateRepresentatives')) {
            $result = $this->updateDefaultRepresentatives();
            if ($result) {
                $output .= $this->displayConfirmation('Obchodní zástupci byli aktualizováni na aktuální tým.');
            } else {
                $output .= $this->displayError('Chyba při aktualizaci obchodních zástupců.');
            }
        }

        if (Tools::isSubmit('updateOffices')) {
            $result = $this->updateDefaultOffices();
            if ($result) {
                $output .= $this->displayConfirmation('Kanceláře byly aktualizovány na výchozí hodnoty.');
            } else {
                $output .= $this->displayError('Chyba při aktualizaci kanceláří.');
            }
        }

        $output .= $this->displayForm();
        $output .= $this->displayShortcodeInfo();
        $output .= $this->displayCmsUpdateButton();
        return $output;
    }

    private function displayForm()
    {
        $representatives = json_decode(Configuration::get('CONTACT_REPRESENTATIVES'), true);
        if (!$representatives) {
            $representatives = [];
        }

        $offices = json_decode(Configuration::get('CONTACT_OFFICES'), true);
        if (!$offices) {
            $offices = [];
        }

        $output = '<div class="panel">
            <div class="panel-heading">
                <i class="icon-cogs"></i> Nastavení kontaktní stránky
            </div>
            <div class="panel-body">
                <form id="configuration_form" class="defaultForm form-horizontal" method="post">
                    <input type="hidden" name="token" value="' . Tools::getAdminTokenLite('AdminModules') . '">

                    <div class="form-group">
                        <label class="control-label col-lg-3">Název společnosti</label>
                        <div class="col-lg-9">
                            <input type="text" name="company_name" value="' . htmlspecialchars(Configuration::get('CONTACT_COMPANY_NAME')) . '" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Adresa</label>
                        <div class="col-lg-9">
                            <textarea name="address" rows="3" class="form-control">' . htmlspecialchars(Configuration::get('CONTACT_ADDRESS')) . '</textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Telefon</label>
                        <div class="col-lg-9">
                            <input type="text" name="phone" value="' . htmlspecialchars(Configuration::get('CONTACT_PHONE')) . '" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Email</label>
                        <div class="col-lg-9">
                            <input type="email" name="email" value="' . htmlspecialchars(Configuration::get('CONTACT_EMAIL')) . '" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Mapa (embed kód)</label>
                        <div class="col-lg-9">
                            <textarea name="map_embed" rows="5" class="form-control" placeholder="Vložte iframe kód z Google Maps nebo Mapy.cz">' . htmlspecialchars($this->getMapEmbedSafe()) . '</textarea>
                            <p class="help-block">Vložte celý iframe kód včetně &lt;iframe&gt; tagů. Podporuje Google Maps i Mapy.cz</p>
                            <div class="alert alert-info" style="margin-top: 10px;">
                                <strong>Debug:</strong> Aktuálně uloženo: ' . (empty($this->getMapEmbedSafe()) ? 'PRÁZDNÉ' : strlen($this->getMapEmbedSafe()) . ' znaků') . '
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Bankovní spojení</label>
                        <div class="col-lg-9">
                            <textarea name="bank_info" rows="3" class="form-control">' . htmlspecialchars(Configuration::get('CONTACT_BANK_INFO')) . '</textarea>
                        </div>
                    </div>

                    <hr>
                    <h4><i class="icon-users"></i> Obchodní zástupci</h4>
                    <p class="help-block">Můžete přetahovat zástupce pro změnu pořadí</p>

                    <div id="representatives-container" class="sortable-container">';

        foreach ($representatives as $index => $rep) {
            $output .= $this->generateRepresentativeForm($index, $rep);
        }

        $output .= '</div>

                    <div class="form-group">
                        <div class="col-lg-offset-3 col-lg-9">
                            <button type="button" id="add-representative" class="btn btn-success">
                                <i class="icon-plus"></i> Přidat zástupce
                            </button>
                        </div>
                    </div>

                    <hr>
                    <h4><i class="icon-building"></i> Kanceláře</h4>
                    <p class="help-block">Můžete přetahovat kanceláře pro změnu pořadí</p>

                    <div id="offices-container" class="sortable-container">';

        foreach ($offices as $index => $office) {
            $output .= $this->generateOfficeForm($index, $office);
        }

        $output .= '</div>

                    <div class="form-group">
                        <div class="col-lg-offset-3 col-lg-9">
                            <button type="button" id="add-office" class="btn btn-success">
                                <i class="icon-plus"></i> Přidat kancelář
                            </button>
                        </div>
                    </div>

                    <div class="panel-footer">
                        <button type="submit" name="submitContactModule" class="btn btn-default pull-right" onclick="debugMapValue()">
                            <i class="process-icon-save"></i> Uložit
                        </button>
                    </div>
                </form>
            </div>
        </div>';

        $output .= $this->getRepresentativeJS();

        return $output;
    }

    private function generateRepresentativeForm($index, $rep = [])
    {
        $name = isset($rep['name']) ? htmlspecialchars($rep['name']) : '';
        $position = isset($rep['position']) ? htmlspecialchars($rep['position']) : '';
        $phone = isset($rep['phone']) ? htmlspecialchars($rep['phone']) : '';
        $email = isset($rep['email']) ? htmlspecialchars($rep['email']) : '';
        $region = isset($rep['region']) ? htmlspecialchars($rep['region']) : '';
        $indexNum = (int)$index + 1;

        return '<div class="representative-form panel panel-default sortable-item" style="margin-bottom: 15px; cursor: move;">
            <div class="panel-heading">
                <i class="icon-move drag-handle" style="margin-right: 10px; cursor: move;"></i>
                <span>Zástupce #' . $indexNum . '</span>
                <button type="button" class="btn btn-danger btn-xs pull-right remove-representative">
                    <i class="icon-trash"></i> Odstranit
                </button>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Jméno</label>
                            <input type="text" name="rep_name[]" value="' . $name . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Pozice</label>
                            <input type="text" name="rep_position[]" value="' . $position . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Oblast</label>
                            <input type="text" name="rep_region[]" value="' . $region . '" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Telefon</label>
                            <input type="text" name="rep_phone[]" value="' . $phone . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="rep_email[]" value="' . $email . '" class="form-control">
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }

    private function generateOfficeForm($index, $office = [])
    {
        $name = isset($office['name']) ? htmlspecialchars($office['name']) : '';
        $address = isset($office['address']) ? htmlspecialchars($office['address']) : '';
        $email = isset($office['email']) ? htmlspecialchars($office['email']) : '';
        $contact_person = isset($office['contact_person']) ? htmlspecialchars($office['contact_person']) : '';
        $phone = isset($office['phone']) ? htmlspecialchars($office['phone']) : '';
        $bank_info = isset($office['bank_info']) ? htmlspecialchars($office['bank_info']) : '';
        $indexNum = (int)$index + 1;

        return '<div class="office-form panel panel-default sortable-item" style="margin-bottom: 15px; cursor: move;">
            <div class="panel-heading">
                <i class="icon-move drag-handle" style="margin-right: 10px; cursor: move;"></i>
                <span>Kancelář #' . $indexNum . '</span>
                <button type="button" class="btn btn-danger btn-xs pull-right remove-office">
                    <i class="icon-trash"></i> Odstranit
                </button>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Název kanceláře</label>
                            <input type="text" name="office_name[]" value="' . $name . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Adresa</label>
                            <textarea name="office_address[]" rows="3" class="form-control">' . $address . '</textarea>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="office_email[]" value="' . $email . '" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Kontaktní osoba</label>
                            <input type="text" name="office_contact_person[]" value="' . $contact_person . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Telefon</label>
                            <input type="text" name="office_phone[]" value="' . $phone . '" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Bankovní informace</label>
                            <textarea name="office_bank_info[]" rows="3" class="form-control">' . $bank_info . '</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }

    private function getRepresentativeJS()
    {
        return '<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
        <script>
        $(document).ready(function() {
            var repIndex = $("#representatives-container .representative-form").length;
            var officeIndex = $("#offices-container .office-form").length;

            // Inicializace Sortable pro drag and drop
            var sortable = Sortable.create(document.getElementById("representatives-container"), {
                handle: ".drag-handle",
                animation: 150,
                ghostClass: "sortable-ghost",
                onEnd: function(evt) {
                    updateRepresentativeNumbers();
                }
            });

            var sortableOffices = Sortable.create(document.getElementById("offices-container"), {
                handle: ".drag-handle",
                animation: 150,
                ghostClass: "sortable-ghost",
                onEnd: function(evt) {
                    updateOfficeNumbers();
                }
            });

            $("#add-representative").click(function() {
                var newForm = `
                <div class="representative-form panel panel-default sortable-item" style="margin-bottom: 15px; cursor: move;">
                    <div class="panel-heading">
                        <i class="icon-move drag-handle" style="margin-right: 10px; cursor: move;"></i>
                        <span>Zástupce #${repIndex + 1}</span>
                        <button type="button" class="btn btn-danger btn-xs pull-right remove-representative">
                            <i class="icon-trash"></i> Odstranit
                        </button>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Jméno</label>
                                    <input type="text" name="rep_name[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Pozice</label>
                                    <input type="text" name="rep_position[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Oblast</label>
                                    <input type="text" name="rep_region[]" value="" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Telefon</label>
                                    <input type="text" name="rep_phone[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="email" name="rep_email[]" value="" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;

                $("#representatives-container").append(newForm);
                repIndex++;
                updateRepresentativeNumbers();
            });

            $(document).on("click", ".remove-representative", function() {
                $(this).closest(".representative-form").remove();
                updateRepresentativeNumbers();
            });

            function updateRepresentativeNumbers() {
                $("#representatives-container .representative-form").each(function(index) {
                    $(this).find(".panel-heading span").text("Zástupce #" + (index + 1));
                });
            }

            // Event handlery pro kanceláře
            $("#add-office").click(function() {
                var newForm = `
                <div class="office-form panel panel-default sortable-item" style="margin-bottom: 15px; cursor: move;">
                    <div class="panel-heading">
                        <i class="icon-move drag-handle" style="margin-right: 10px; cursor: move;"></i>
                        <span>Kancelář #${officeIndex + 1}</span>
                        <button type="button" class="btn btn-danger btn-xs pull-right remove-office">
                            <i class="icon-trash"></i> Odstranit
                        </button>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Název kanceláře</label>
                                    <input type="text" name="office_name[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Adresa</label>
                                    <textarea name="office_address[]" rows="3" class="form-control"></textarea>
                                </div>
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="email" name="office_email[]" value="" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Kontaktní osoba</label>
                                    <input type="text" name="office_contact_person[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Telefon</label>
                                    <input type="text" name="office_phone[]" value="" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label>Bankovní informace</label>
                                    <textarea name="office_bank_info[]" rows="3" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;

                $("#offices-container").append(newForm);
                officeIndex++;
                updateOfficeNumbers();
            });

            $(document).on("click", ".remove-office", function() {
                $(this).closest(".office-form").remove();
                updateOfficeNumbers();
            });

            function updateOfficeNumbers() {
                $("#offices-container .office-form").each(function(index) {
                    $(this).find(".panel-heading span").text("Kancelář #" + (index + 1));
                });
            }
        });

        // Debug funkce pro mapu
        function debugMapValue() {
            var mapValue = document.querySelector(\'textarea[name="map_embed"]\').value;
            console.log("Mapa před odesláním:", mapValue);
            console.log("Délka mapy:", mapValue.length);
            if (mapValue.length === 0) {
                alert("POZOR: Mapa je prázdná!");
                return false;
            }
            return true;
        }
        </script>';
    }

    private function displayShortcodeInfo()
    {
        return '<div class="alert alert-info">
            <h4><i class="icon-info-circle"></i> Jak použít modul</h4>
            <p>Pro zobrazení kontaktních informací na CMS stránce použijte shortcode:</p>
            <code>[contactmodule]</code>
            <p>Tento shortcode vložte do obsahu CMS stránky a automaticky se nahradí kontaktními informacemi.</p>
        </div>';
    }

    private function displayCmsUpdateButton()
    {
        $cmsMinimalPath = $this->local_path . 'cms-minimal.html';
        $cmsMinimalExists = file_exists($cmsMinimalPath);

        $output = '<div class="alert alert-warning">
            <h4><i class="icon-file-text"></i> Aktualizace CMS stránky</h4>';

        if ($cmsMinimalExists) {
            $output .= '<p>Můžete automaticky aktualizovat obsah CMS stránky s ID 22 (Kontakty) obsahem z cms-minimal.html:</p>
                <form method="post" style="display: inline;">
                    <button type="submit" name="updateCmsPage" class="btn btn-warning" onclick="return confirm(\'Opravdu chcete přepsat obsah CMS stránky?\')">
                        <i class="icon-refresh"></i> Aktualizovat CMS stránku
                    </button>
                </form>
                <form method="post" style="display: inline; margin-left: 10px;">
                    <button type="submit" name="insertShortcode" class="btn btn-info" onclick="return confirm(\'Vložit shortcode [contactmodule] do CMS stránky?\')">
                        <i class="icon-code"></i> Vložit shortcode
                    </button>
                </form>
                <form method="post" style="display: inline; margin-left: 10px;">
                    <button type="submit" name="updateRepresentatives" class="btn btn-success" onclick="return confirm(\'Aktualizovat obchodní zástupce na aktuální tým?\')">
                        <i class="icon-users"></i> Aktualizovat tým
                    </button>
                </form>
                <form method="post" style="display: inline; margin-left: 10px;">
                    <button type="submit" name="updateOffices" class="btn btn-info" onclick="return confirm(\'Aktualizovat kanceláře na výchozí hodnoty?\')">
                        <i class="icon-building"></i> Aktualizovat kanceláře
                    </button>
                </form>
                <form method="post" style="display: inline; margin-left: 10px;">
                    <button type="submit" name="resetToDefaults" class="btn btn-warning" onclick="return confirm(\'Opravdu chcete obnovit všechna data na výchozí hodnoty? Ztratíte všechny aktuální změny!\')">
                        <i class="icon-refresh"></i> Obnovit výchozí
                    </button>
                </form>';
        } else {
            $output .= '<p>Soubor cms-minimal.html nebyl nalezen.</p>';
        }

        $output .= '</div>';

        return $output;
    }

    private function updateCmsPageContent()
    {
        $cmsMinimalPath = $this->local_path . 'cms-minimal.html';

        if (!file_exists($cmsMinimalPath)) {
            return false;
        }

        $content = file_get_contents($cmsMinimalPath);
        if ($content === false) {
            return false;
        }

        // Aktualizuje obsah s aktuálními daty z konfigurace
        $content = $this->replacePlaceholdersInContent($content);

        // Najde CMS stránku s ID 22
        $cms = new CMS(22);
        if (!Validate::isLoadedObject($cms)) {
            return false;
        }

        // Aktualizuje obsah pro všechny jazyky
        $languages = Language::getLanguages(false);
        foreach ($languages as $language) {
            $cms->content[$language['id_lang']] = $content;
        }

        return $cms->save();
    }

    private function insertShortcodeIntoCmsPage()
    {
        // Najde CMS stránku s ID 22
        $cms = new CMS(22);
        if (!Validate::isLoadedObject($cms)) {
            return false;
        }

        $shortcode = '[contactmodule]';

        // Aktualizuje obsah pro všechny jazyky
        $languages = Language::getLanguages(false);
        foreach ($languages as $language) {
            $currentContent = $cms->content[$language['id_lang']];

            // Pokud shortcode už není v obsahu, přidá ho
            if (strpos($currentContent, $shortcode) === false) {
                $cms->content[$language['id_lang']] = $currentContent . "\n\n" . $shortcode;
            }
        }

        return $cms->save();
    }

    private function replacePlaceholdersInContent($content)
    {
        $company_name = Configuration::get('CONTACT_COMPANY_NAME');
        $address = Configuration::get('CONTACT_ADDRESS');
        $phone = Configuration::get('CONTACT_PHONE');
        $email = Configuration::get('CONTACT_EMAIL');
        $map_embed = Configuration::get('CONTACT_MAP_EMBED');
        $bank_info = Configuration::get('CONTACT_BANK_INFO');
        $representatives = json_decode(Configuration::get('CONTACT_REPRESENTATIVES'), true);

        // Nahradí základní informace
        $content = str_replace('CZECH IMAGE GROUP s.r.o.', htmlspecialchars($company_name), $content);
        $content = str_replace('Sladovnická 508/19<br>620 00 Brno<br>Česká republika', $address, $content);
        $content = str_replace('+*********** 780', htmlspecialchars($phone), $content);
        $content = str_replace('<EMAIL>', htmlspecialchars($email), $content);
        $content = str_replace('Účet: *********/0100<br>IBAN: CZ*********0*********', $bank_info, $content);

        // Nahradí mapu
        if ($map_embed) {
            $pattern = '/<iframe[^>]*src="[^"]*google\.com\/maps[^"]*"[^>]*><\/iframe>/';
            $content = preg_replace($pattern, $map_embed, $content);
        }

        // Nahradí obchodní zástupce
        if (!empty($representatives)) {
            $content = $this->replaceRepresentativesInContent($content, $representatives);
        }

        return $content;
    }

    private function replaceRepresentativesInContent($content, $representatives)
    {
        // Najde sekci s obchodními zástupci
        $pattern = '/(<div[^>]*grid-template-columns[^>]*>)(.*?)(<\/div>\s*<\/div>)/s';

        if (preg_match($pattern, $content, $matches)) {
            $newRepresentatives = '';

            foreach ($representatives as $rep) {
                if (!empty($rep['name'])) {
                    $newRepresentatives .= '
                        <div style="background: #fff; border: 1px solid #e0e0e0; border-radius: 8px; padding: 25px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                                <h4 style="color: #007acc; font-size: 18px; margin-bottom: 5px;">' . htmlspecialchars($rep['name']) . '</h4>
                                <p style="color: #666; font-style: italic; margin: 0;">' . htmlspecialchars($rep['position']) . '</p>
                            </div>
                            <div>
                                <div style="margin-bottom: 10px;">
                                    <strong>Telefon:</strong> <a href="tel:' . str_replace(' ', '', $rep['phone']) . '" style="color: #007acc; text-decoration: none;">' . htmlspecialchars($rep['phone']) . '</a>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <strong>Email:</strong> <a href="mailto:' . htmlspecialchars($rep['email']) . '" style="color: #007acc; text-decoration: none;">' . htmlspecialchars($rep['email']) . '</a>
                                </div>
                                <div style="color: #666; font-size: 14px;">
                                    <strong>Oblast:</strong> ' . htmlspecialchars($rep['region']) . '
                                </div>
                            </div>
                        </div>';
                }
            }

            $content = str_replace($matches[0], $matches[1] . $newRepresentatives . $matches[3], $content);
        }

        return $content;
    }

    public function getContactPageContent()
    {
        $representatives = json_decode(Configuration::get('CONTACT_REPRESENTATIVES'), true);
        if (!$representatives) {
            $representatives = [];
        }

        $offices = json_decode(Configuration::get('CONTACT_OFFICES'), true);
        if (!$offices) {
            $offices = [];
        }

        $map_embed_raw = $this->getMapEmbedSafe();

        $this->context->smarty->assign([
            'company_name' => Configuration::get('CONTACT_COMPANY_NAME'),
            'address' => Configuration::get('CONTACT_ADDRESS'),
            'phone' => Configuration::get('CONTACT_PHONE'),
            'email' => Configuration::get('CONTACT_EMAIL'),
            'map_embed' => $this->processMapEmbed($map_embed_raw),
            'bank_info' => Configuration::get('CONTACT_BANK_INFO'),
            'representatives' => $representatives,
            'offices' => $offices
        ]);

        return $this->context->smarty->fetch($this->local_path . 'views/templates/front/contact.tpl');
    }

    public function getContactPageContentWithInlineStyles()
    {
        $company_name = Configuration::get('CONTACT_COMPANY_NAME');
        $address = Configuration::get('CONTACT_ADDRESS');
        $phone = Configuration::get('CONTACT_PHONE');
        $email = Configuration::get('CONTACT_EMAIL');
        $map_embed = $this->getMapEmbedSafe();
        $bank_info = Configuration::get('CONTACT_BANK_INFO');
        $representatives = json_decode(Configuration::get('CONTACT_REPRESENTATIVES'), true);
        $offices = json_decode(Configuration::get('CONTACT_OFFICES'), true);

        if (!$representatives) {
            $representatives = [];
        }

        if (!$offices) {
            $offices = [];
        }

        $content = '<div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
            <div style="display: flex; gap: 30px; margin-bottom: 40px; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 300px;">
                    <h3 style="color: #333; margin-bottom: 20px; font-size: 24px; border-bottom: 2px solid #007acc; padding-bottom: 10px;">Kontaktní informace</h3>
                    <div>
                        <h4 style="color: #007acc; font-size: 20px; margin-bottom: 15px;">' . htmlspecialchars($company_name) . '</h4>
                        <div style="margin-bottom: 20px; line-height: 1.6; color: #666;">
                            ' . nl2br(htmlspecialchars($address)) . '
                        </div>
                        <div style="margin-bottom: 25px;">
                            <div style="margin-bottom: 10px;">
                                <strong>Telefon:</strong> <a href="tel:' . str_replace(' ', '', $phone) . '" style="color: #007acc; text-decoration: none;">' . htmlspecialchars($phone) . '</a>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <strong>Email:</strong> <a href="mailto:' . htmlspecialchars($email) . '" style="color: #007acc; text-decoration: none;">' . htmlspecialchars($email) . '</a>
                            </div>
                        </div>';

        if ($bank_info) {
            $content .= '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc;">
                            <h5 style="margin-bottom: 10px; color: #333;">Bankovní informace</h5>
                            <div style="line-height: 1.6; color: #666;">
                                ' . nl2br(htmlspecialchars($bank_info)) . '
                            </div>
                        </div>';
        }

        $content .= '</div>
                </div>

                <div style="flex: 1; min-width: 300px;">
                    <h3 style="color: #333; margin-bottom: 20px; font-size: 24px; border-bottom: 2px solid #007acc; padding-bottom: 10px;">Kde nás najdete</h3>
                    <div style="border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); position: relative; width: 100%; height: 400px;">
                        ' . (!empty($map_embed) ? $this->processMapEmbed($map_embed) : '<p style="text-align: center; color: #666; padding: 50px;">Mapa není k dispozici.</p>') . '
                    </div>
                </div>
            </div>';

        if (!empty($representatives)) {
            $content .= '<div style="margin-top: 40px;">
                <h3 style="color: #333; margin-bottom: 20px; font-size: 24px; border-bottom: 2px solid #007acc; padding-bottom: 10px;">Obchodní zástupci</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-top: 20px;">';

            foreach ($representatives as $rep) {
                if (!empty($rep['name'])) {
                    $content .= '<div style="background: #fff; border: 1px solid #e0e0e0; border-radius: 8px; padding: 25px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                        <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                            <h4 style="color: #007acc; font-size: 18px; margin-bottom: 5px;">' . htmlspecialchars($rep['name']) . '</h4>';

                    if (!empty($rep['position'])) {
                        $content .= '<p style="color: #666; font-style: italic; margin: 0;">' . htmlspecialchars($rep['position']) . '</p>';
                    }

                    $content .= '</div>
                        <div>';

                    if (!empty($rep['phone'])) {
                        $content .= '<div style="margin-bottom: 10px;">
                                <strong>Telefon:</strong> <a href="tel:' . str_replace(' ', '', $rep['phone']) . '" style="color: #007acc; text-decoration: none;">' . htmlspecialchars($rep['phone']) . '</a>
                            </div>';
                    }

                    if (!empty($rep['email'])) {
                        $content .= '<div style="margin-bottom: 10px;">
                                <strong>Email:</strong> <a href="mailto:' . htmlspecialchars($rep['email']) . '" style="color: #007acc; text-decoration: none;">' . htmlspecialchars($rep['email']) . '</a>
                            </div>';
                    }

                    if (!empty($rep['region'])) {
                        $content .= '<div style="color: #666; font-size: 14px;">
                                <strong>Oblast:</strong> ' . htmlspecialchars($rep['region']) . '
                            </div>';
                    }

                    $content .= '</div>
                    </div>';
                }
            }

            $content .= '</div>
            </div>';
        }

        // Přidání kanceláří
        if (!empty($offices)) {
            $content .= '<div style="margin-top: 40px;">
                <h3 style="color: #333; margin-bottom: 20px; font-size: 24px; border-bottom: 2px solid #007acc; padding-bottom: 10px;">Kanceláře</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-top: 20px;">';

            foreach ($offices as $office) {
                if (!empty($office['name'])) {
                    $content .= '<div style="background: #fff; border: 1px solid #e0e0e0; border-radius: 8px; padding: 25px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                        <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                            <h4 style="color: #007acc; font-size: 18px; margin-bottom: 5px;">' . htmlspecialchars($office['name']) . '</h4>';

                    if (!empty($office['address'])) {
                        $content .= '<div style="color: #666; margin-bottom: 10px; line-height: 1.6;">
                                ' . nl2br(htmlspecialchars($office['address'])) . '
                            </div>';
                    }

                    $content .= '</div>
                        <div>';

                    if (!empty($office['contact_person'])) {
                        $content .= '<div style="margin-bottom: 10px;">
                                <strong>Kontaktní osoba:</strong> ' . htmlspecialchars($office['contact_person']) . '
                            </div>';
                    }

                    if (!empty($office['phone'])) {
                        $content .= '<div style="margin-bottom: 10px;">
                                <strong>Telefon:</strong> <a href="tel:' . str_replace(' ', '', $office['phone']) . '" style="color: #007acc; text-decoration: none;">' . htmlspecialchars($office['phone']) . '</a>
                            </div>';
                    }

                    if (!empty($office['email'])) {
                        $content .= '<div style="margin-bottom: 10px;">
                                <strong>Email:</strong> <a href="mailto:' . htmlspecialchars($office['email']) . '" style="color: #007acc; text-decoration: none;">' . htmlspecialchars($office['email']) . '</a>
                            </div>';
                    }

                    if (!empty($office['bank_info'])) {
                        $content .= '<div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-top: 15px; border-left: 3px solid #007acc;">
                                <strong style="color: #333;">Bankovní spojení:</strong><br>
                                <div style="color: #666; font-size: 14px; line-height: 1.6; margin-top: 5px;">
                                    ' . nl2br(htmlspecialchars($office['bank_info'])) . '
                                </div>
                            </div>';
                    }

                    $content .= '</div>
                    </div>';
                }
            }

            $content .= '</div>
            </div>';
        }

        $content .= '</div>';

        return $content;
    }

    public function getMapEmbedSafe()
    {
        // Zkusí načíst z base64 verze
        $encoded = Configuration::get('CONTACT_MAP_EMBED_ENCODED');
        if (!empty($encoded)) {
            $decoded = base64_decode($encoded);
            if ($decoded !== false && strpos($decoded, '<iframe') !== false) {
                return $decoded;
            }
        }

        // Fallback na normální verzi
        return Configuration::get('CONTACT_MAP_EMBED');
    }

    public function processMapEmbed($mapEmbed)
    {
        if (empty($mapEmbed)) {
            return '<p style="text-align: center; color: #666; padding: 50px;">Mapa není k dispozici.</p>';
        }

        // Upraví iframe pro správné zobrazení
        if (strpos($mapEmbed, '<iframe') !== false) {
            // Odstraní existující width, height a style atributy
            $mapEmbed = preg_replace('/width="[^"]*"/', '', $mapEmbed);
            $mapEmbed = preg_replace('/height="[^"]*"/', '', $mapEmbed);
            $mapEmbed = preg_replace('/style="[^"]*"/', '', $mapEmbed);

            // Přidá nové styly pro plné zobrazení
            $mapEmbed = str_replace('<iframe', '<iframe style="width: 100%; height: 100%; border: 0; display: block;"', $mapEmbed);
        }

        return $mapEmbed;
    }

    public function hookDisplayHeader()
    {
        // Přidá JavaScript pro nahrazování shortcode
        $js = '
        <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Najde všechny elementy obsahující shortcode
            var elements = document.querySelectorAll("*");
            elements.forEach(function(element) {
                if (element.innerHTML && element.innerHTML.includes("[contactmodule]")) {
                    // AJAX volání pro získání obsahu modulu
                    fetch("' . $this->context->link->getModuleLink($this->name, 'ajax') . '", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded",
                        },
                        body: "action=getContent"
                    })
                    .then(response => response.text())
                    .then(data => {
                        element.innerHTML = element.innerHTML.replace("[contactmodule]", data);
                    })
                    .catch(error => console.error("Error:", error));
                }
            });
        });
        </script>';
        return $js;
    }

    public function hookFilterCmsContent($params)
    {
        if (isset($params['object']) && isset($params['object']->content)) {
            $content = $params['object']->content;
            if (strpos($content, '[contactmodule]') !== false) {
                $contactContent = $this->getContactPageContentWithInlineStyles();
                $content = str_replace('[contactmodule]', $contactContent, $content);
                $params['object']->content = $content;
            }
        }
        return $params;
    }

    public function hookActionOutputHTMLBefore($params)
    {
        if (isset($params['html'])) {
            $html = $params['html'];
            if (strpos($html, '[contactmodule]') !== false) {
                $contactContent = $this->getContactPageContentWithInlineStyles();
                $html = str_replace('[contactmodule]', $contactContent, $html);
                $params['html'] = $html;
            }
        }
        return null;
    }

    // Statická metoda pro snadné volání z šablon
    public static function getContactContent()
    {
        $module = Module::getInstanceByName('contactmodule');
        if ($module && $module->active) {
            return $module->getContactPageContent();
        }
        return '';
    }
}
