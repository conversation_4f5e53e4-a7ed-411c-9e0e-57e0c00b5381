/* Orders Management Styles - CIG Realizace Phase 04 */

/* Page Header */
.orders-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
}

.orders-header h1 {
    margin-bottom: 10px;
    font-weight: 600;
}

.orders-header .breadcrumb {
    background: transparent;
    margin-bottom: 0;
    padding: 0;
}

.orders-header .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.orders-header .breadcrumb-item.active {
    color: white;
}

/* Filters Section */
.filters-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 30px;
}

.filters-section h5 {
    color: #495057;
    margin-bottom: 20px;
    font-weight: 600;
}

.filter-row {
    margin-bottom: 15px;
}

.filter-row:last-child {
    margin-bottom: 0;
}

/* Orders Table */
.orders-table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.orders-table {
    margin-bottom: 0;
}

.orders-table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 15px 12px;
    vertical-align: middle;
}

.orders-table tbody td {
    padding: 12px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.orders-table tbody tr:hover {
    background-color: #f8f9fa;
}

.orders-table tbody tr:last-child td {
    border-bottom: none;
}

/* Order Code Links */
.order-code-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s;
}

.order-code-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Status Badges */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    text-align: center;
    min-width: 100px;
    display: inline-block;
}

.status-not-created {
    background-color: #dc3545;
    color: white;
}

.status-sent-to-client {
    background-color: #ffc107;
    color: #212529;
}

.status-approved {
    background-color: #28a745;
    color: white;
}

.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.status-in-progress {
    background-color: #17a2b8;
    color: white;
}

.status-completed {
    background-color: #28a745;
    color: white;
}

.status-cancelled {
    background-color: #6c757d;
    color: white;
}

/* Sales Rep Info */
.sales-rep-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sales-rep-prefix {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.sales-rep-name {
    color: #495057;
    font-weight: 500;
}

/* Items Count */
.items-count {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 600;
    min-width: 40px;
    text-align: center;
    display: inline-block;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 6px;
    font-size: 0.8em;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.action-btn-primary {
    background: #667eea;
    color: white;
}

.action-btn-primary:hover {
    background: #5a6fd8;
    color: white;
}

.action-btn-success {
    background: #28a745;
    color: white;
}

.action-btn-success:hover {
    background: #218838;
    color: white;
}

.action-btn-warning {
    background: #ffc107;
    color: #212529;
}

.action-btn-warning:hover {
    background: #e0a800;
    color: #212529;
}

/* Statistics Cards */
.stats-cards {
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 20px;
    text-align: center;
    transition: transform 0.3s;
    border: none;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Pagination */
.pagination-info {
    background: white;
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 0.9rem;
}

.pagination .page-link {
    color: #667eea;
    border-color: #dee2e6;
    padding: 8px 12px;
}

.pagination .page-link:hover {
    color: #764ba2;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}

.empty-state h4 {
    margin-bottom: 10px;
    color: #495057;
}

.empty-state p {
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .orders-header {
        padding: 20px 0;
    }
    
    .filters-section {
        padding: 20px 15px;
    }
    
    .orders-table-container {
        overflow-x: auto;
    }
    
    .orders-table {
        min-width: 800px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }
    
    .action-btn {
        font-size: 0.75em;
        padding: 4px 8px;
    }
    
    .sales-rep-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .orders-header h1 {
        font-size: 1.5rem;
    }
    
    .filters-section {
        padding: 15px 10px;
    }
    
    .stat-card .stat-icon {
        font-size: 2rem;
    }
    
    .stat-card .stat-number {
        font-size: 1.5rem;
    }
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sort Indicators */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sortable:hover {
    background-color: #e9ecef;
}

.sortable.sorted-asc::after {
    content: " ↑";
    color: #667eea;
    font-weight: bold;
}

.sortable.sorted-desc::after {
    content: " ↓";
    color: #667eea;
    font-weight: bold;
}

/* Order Detail Expansion */
.order-detail-row {
    background-color: #f8f9fa;
}

.order-detail-container {
    padding: 20px;
    border-top: 2px solid #dee2e6;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
    to {
        opacity: 1;
        max-height: 500px;
        padding-top: 20px;
        padding-bottom: 20px;
    }
}

.expand-icon {
    transition: transform 0.3s ease;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

.order-code-link {
    color: #667eea !important;
    text-decoration: none !important;
    font-weight: 600;
    border: none !important;
    background: none !important;
    display: flex;
    align-items: center;
}

.order-code-link:hover {
    color: #764ba2 !important;
}

.order-code-link:focus {
    box-shadow: none !important;
}

.loading-placeholder {
    text-align: center;
    color: #6c757d;
    padding: 20px;
}

/* Order Detail Content */
.order-detail-content {
    display: none;
}

.order-detail-content.loaded {
    display: block;
}

.detail-section {
    margin-bottom: 25px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.detail-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.detail-info-item {
    display: flex;
    flex-direction: column;
}

.detail-info-label {
    font-size: 0.85em;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
}

.detail-info-value {
    font-weight: 600;
    color: #495057;
}

.items-table {
    font-size: 0.9em;
}

.items-table th {
    background-color: #e9ecef;
    font-weight: 600;
    color: #495057;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
}

.items-table td {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
}

.items-table .irrelevant {
    opacity: 0.6;
    text-decoration: line-through;
}

.quick-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-action-btn {
    padding: 6px 12px;
    font-size: 0.85em;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.quick-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.quick-action-primary {
    background: #667eea;
    color: white;
}

.quick-action-primary:hover {
    background: #5a6fd8;
    color: white;
}

.quick-action-warning {
    background: #ffc107;
    color: #212529;
}

.quick-action-warning:hover {
    background: #e0a800;
    color: #212529;
}

.quick-action-success {
    background: #28a745;
    color: white;
}

.quick-action-success:hover {
    background: #218838;
    color: white;
}
