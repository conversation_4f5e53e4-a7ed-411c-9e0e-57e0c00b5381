<?php
/**
 * AJAX endpoint for getting order detail
 * CIG Realizace - Phase 04
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check
require_once '../includes/auth_check.php';

// Check if user is logged in
$current_user = getCurrentUser();
if (!$current_user) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášeni']);
    exit;
}

// Check if request is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

// Get and validate input
$order_id = intval($_GET['id'] ?? 0);

if (!$order_id) {
    echo json_encode(['success' => false, 'message' => 'Neplatné ID objednávky']);
    exit;
}

try {
    $pdo = getDbConnection();
    
    // Get order details
    $stmt = $pdo->prepare("
        SELECT o.*, 
               COUNT(oi.id) as items_count
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.is_relevant = 1
        WHERE o.id = ?
        GROUP BY o.id
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo json_encode(['success' => false, 'message' => 'Objednávka nebyla nalezena']);
        exit;
    }
    
    // Check if user has permission to view this order
    $user_role = $current_user['role'];
    if ($user_role === 'obchodnik') {
        // Sales rep can only see their own orders
        // This would need proper mapping implementation
        // For now, we'll allow all orders for testing
    }
    
    // Get order items
    $stmt = $pdo->prepare("
        SELECT * FROM order_items 
        WHERE order_id = ? 
        ORDER BY catalog_code, id
    ");
    $stmt->execute([$order_id]);
    $order_items = $stmt->fetchAll();
    
    // Format dates for JSON
    $order['order_date'] = $order['order_date'];
    $order['created_at'] = $order['created_at'];
    $order['updated_at'] = $order['updated_at'];
    $order['preview_approved_date'] = $order['preview_approved_date'];
    $order['expected_delivery_date'] = $order['expected_delivery_date'];
    $order['completed_date'] = $order['completed_date'];
    
    // Format order items
    foreach ($order_items as &$item) {
        $item['goods_ordered_date'] = $item['goods_ordered_date'];
        $item['goods_stocked_date'] = $item['goods_stocked_date'];
        $item['created_at'] = $item['created_at'];
        $item['updated_at'] = $item['updated_at'];
        
        // Convert numeric fields
        $item['quantity'] = floatval($item['quantity']);
        $item['is_relevant'] = intval($item['is_relevant']);
    }
    
    echo json_encode([
        'success' => true,
        'order' => $order,
        'items' => $order_items
    ]);
    
} catch (Exception $e) {
    error_log("Error getting order detail: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při načítání detailu objednávky: ' . $e->getMessage()
    ]);
}
?>
