<?php
/**
 * Admin Controller for Katalogy Module - OPRAVA POSITION SYSTÉMU
 */

require_once(_PS_MODULE_DIR_ . 'katalogy/classes/Katalog.php');

class AdminKatalogyController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'katalogy';
        $this->className = 'Katalog';
        $this->lang = false;
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->identifier = 'id_katalog';

        parent::__construct();

        $this->fields_list = [
            'id_katalog' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'image' => [
                'title' => $this->l('Obrázek'),
                'align' => 'center',
                'image' => 'katalogy',
                'orderby' => false,
                'search' => false,
                'class' => 'fixed-width-xs'
            ],
            'title' => [
                'title' => $this->l('Název'),
                'width' => 'auto'
            ],
            'description' => [
                'title' => $this->l('Popis'),
                'width' => 'auto',
                'maxlength' => 100
            ],
            'is_new' => [
                'title' => $this->l('Nový'),
                'align' => 'center',
                'type' => 'bool',
                'class' => 'fixed-width-xs'
            ],
            'position' => [
                'title' => $this->l('Pozice'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'position' => 'position' // Klíčové pro drag & drop
            ],
            'active' => [
                'title' => $this->l('Aktivní'),
                'align' => 'center',
                'type' => 'bool',
                'class' => 'fixed-width-xs'
            ],
            'date_add' => [
                'title' => $this->l('Vytvořeno'),
                'align' => 'center',
                'type' => 'datetime',
                'class' => 'fixed-width-lg'
            ]
        ];

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Smazat vybrané'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Smazat vybrané položky?')
            ]
        ];

        $this->fields_form = [
            'legend' => [
                'title' => $this->l('Katalog'),
                'icon' => 'icon-folder-open'
            ],
            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Název'),
                    'name' => 'title',
                    'required' => true,
                    'size' => 50
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Popis'),
                    'name' => 'description',
                    'rows' => 5,
                    'cols' => 50
                ],
                [
                    'type' => 'file',
                    'label' => $this->l('Obrázek'),
                    'name' => 'image',
                    'desc' => $this->l('Nahrajte náhledový obrázek katalogu')
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('URL katalogu'),
                    'name' => 'file_url',
                    'size' => 100,
                    'desc' => $this->l('Zadejte URL pro stažení katalogu')
                ],
                [
                    'type' => 'file',
                    'label' => $this->l('Soubor katalogu'),
                    'name' => 'catalog_file',
                    'desc' => $this->l('Nebo nahrajte soubor katalogu')
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Nový katalog'),
                    'name' => 'is_new',
                    'values' => [
                        [
                            'id' => 'is_new_on',
                            'value' => 1,
                            'label' => $this->l('Ano')
                        ],
                        [
                            'id' => 'is_new_off',
                            'value' => 0,
                            'label' => $this->l('Ne')
                        ]
                    ]
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Pozice'),
                    'name' => 'position',
                    'size' => 5,
                    'desc' => $this->l('Pořadí zobrazení (nižší číslo = vyšší pozice)')
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Aktivní'),
                    'name' => 'active',
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Ano')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Ne')
                        ]
                    ]
                ]
            ],
            'submit' => [
                'title' => $this->l('Uložit')
            ]
        ];

        // ⭐ ZÁKLADNÍ NASTAVENÍ PRO POZICE
        $this->_orderBy = 'position';
        $this->_orderWay = 'ASC';
        $this->position_identifier = 'id_katalog';
    }

    /**
     * ⭐ OVERRIDE: Přidání pozičních šipek do každého řádku
     */
    public function renderList()
    {
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->addRowAction('view');

        $this->enable_drag_and_drop = true;

        return parent::renderList();
    }

    

    /**
     * ⭐ KLÍČOVÁ METODA: updatePositions pro drag & drop
     */
    public function ajaxProcessUpdatePositions()
    {
        $log_file = _PS_MODULE_DIR_ . 'katalogy/katalogy_debug.log';
        file_put_contents($log_file, "=== PRESTASHOP POSITION UPDATE ===\n", FILE_APPEND);
        file_put_contents($log_file, "POST: " . print_r($_POST, true) . "\n", FILE_APPEND);
        file_put_contents($log_file, "GET: " . print_r($_GET, true) . "\n", FILE_APPEND);

        $positions = Tools::getValue('katalog'); // Explicitně získej 'katalog' z POST dat
        file_put_contents($log_file, "Received positions via Tools::getValue('katalog'): " . print_r($positions, true) . "\n", FILE_APPEND);

        if (!$positions || !is_array($positions)) {
            file_put_contents($log_file, "❌ No positions data or not an array. Raw POST: " . print_r($_POST, true) . "\n", FILE_APPEND);
            die(json_encode(['hasError' => true, 'errors' => ['No positions data']]));
        }

        try {
            Db::getInstance()->execute('START TRANSACTION');
            $updated_count = 0;
            
            foreach ($positions as $position => $value) {
                file_put_contents($log_file, "Processing: position=$position, value=$value\n", FILE_APPEND);
                
                // Extrakce ID z formátu tr_SHOPID_ID_POSITION
                $parts = explode('_', $value);
                $katalog_id = (int)$parts[2]; // ID je třetí část (index 2)
                
                if ($katalog_id > 0) {
                    $db_position = (int)$position + 1; // 0-based to 1-based
                    
                    file_put_contents($log_file, "Attempting to update katalog $katalog_id to position $db_position\n", FILE_APPEND);

                    $sql = 'UPDATE `' . _DB_PREFIX_ . 'katalogy` 
                           SET `position` = ' . (int)$db_position . ',
                               `date_upd` = NOW()
                           WHERE `id_katalog` = ' . (int)$katalog_id;
                    
                    if (Db::getInstance()->execute($sql)) {
                        file_put_contents($log_file, "✅ Updated katalog $katalog_id to position $db_position\n", FILE_APPEND);
                        $updated_count++;
                    } else {
                        file_put_contents($log_file, "❌ Failed to update katalog $katalog_id\n", FILE_APPEND);
                    }
                } else {
                    file_put_contents($log_file, "❌ Could not extract valid ID from: $value\n", FILE_APPEND);
                }
            }
            
            if ($updated_count > 0) {
                Db::getInstance()->execute('COMMIT');
                Katalog::fixDuplicatePositions(); // Oprav pozice po úspěšné aktualizaci
                file_put_contents($log_file, "=== SUCCESS: $updated_count positions updated ===\n", FILE_APPEND);
                
                die(json_encode(['hasError' => false, 'updated' => $updated_count]));
            } else {
                throw new Exception("No positions were updated");
            }
            
        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            file_put_contents($log_file, "❌ ERROR: " . $e->getMessage() . "\n", FILE_APPEND);
            die(json_encode(['hasError' => true, 'errors' => [$e->getMessage()]]));
        }
    }

    /**
     * ⭐ KLÍČOVÁ METODA: processPosition pro šipky ↑↓
     */
    public function processPosition()
    {
        $log_file = _PS_MODULE_DIR_ . 'katalogy/katalogy_debug.log';
        file_put_contents($log_file, "=== PROCESS POSITION START ===\n", FILE_APPEND);
        
        if (!$this->loadObject(true)) {
            file_put_contents($log_file, "❌ Could not load object\n", FILE_APPEND);
            return false;
        }

        $way = (int)Tools::getValue('way');
        $katalog = $this->object;
        $current_position = (int)$katalog->position;
        
        file_put_contents($log_file, "ProcessPosition: way=$way, current_position=$current_position, katalog_id=" . $katalog->id . "\n", FILE_APPEND);
        
        if ($way) {
            // Nahoru (snížit pozici)
            $new_position = max(1, $current_position - 1);
        } else {
            // Dolů (zvýšit pozici)
            $new_position = $current_position + 1;
        }

        if ($current_position == $new_position) {
            file_put_contents($log_file, "⚠️ No position change needed\n", FILE_APPEND);
            $this->redirect_after = self::$currentIndex . '&token=' . $this->token;
            return true;
        }

        try {
            Db::getInstance()->execute('START TRANSACTION');
            
            // Najdi katalog na cílové pozici
            $swap_sql = 'SELECT `id_katalog`, `position` FROM `' . _DB_PREFIX_ . 'katalogy` 
                        WHERE `position` = ' . (int)$new_position . ' 
                        AND `id_katalog` != ' . (int)$katalog->id . ' 
                        LIMIT 1';
            $swap_katalog = Db::getInstance()->getRow($swap_sql);
            
            if ($swap_katalog) {
                // Prohoď pozice
                $update1 = 'UPDATE `' . _DB_PREFIX_ . 'katalogy` 
                           SET `position` = ' . (int)$current_position . ',
                               `date_upd` = NOW()
                           WHERE `id_katalog` = ' . (int)$swap_katalog['id_katalog'];
                
                if (!Db::getInstance()->execute($update1)) {
                    throw new Exception('Failed to update swap katalog position');
                }
                
                file_put_contents($log_file, "✅ Swapped katalog " . $swap_katalog['id_katalog'] . " from position $new_position to $current_position\n", FILE_APPEND);
            }
            
            // Aktualizuj pozici aktuálního katalogu
            $update2 = 'UPDATE `' . _DB_PREFIX_ . 'katalogy` 
                       SET `position` = ' . (int)$new_position . ',
                           `date_upd` = NOW()
                       WHERE `id_katalog` = ' . (int)$katalog->id;
            
            if (Db::getInstance()->execute($update2)) {
                Db::getInstance()->execute('COMMIT');
                file_put_contents($log_file, "✅ Moved katalog " . $katalog->id . " from position $current_position to $new_position\n", FILE_APPEND);
                
                $this->confirmations[] = $this->l('Pozice byla úspěšně změněna.');
                $this->redirect_after = self::$currentIndex . '&token=' . $this->token . '&conf=5';
                return true;
            } else {
                throw new Exception('Failed to update katalog position');
            }
            
        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            file_put_contents($log_file, "❌ ProcessPosition error: " . $e->getMessage() . "\n", FILE_APPEND);
            $this->errors[] = $this->l('Chyba při změně pozice: ') . $e->getMessage();
            return false;
        }
    }

    public function renderForm()
    {
        if (isset($this->object) && $this->object->id) {
            $katalog = new Katalog($this->object->id);
            
            if ($katalog->image) {
                $image_url = _MODULE_DIR_ . 'katalogy/views/img/katalogy/' . $katalog->image;
                $current_image_input = [
                    'type' => 'html',
                    'label' => $this->l('Aktuální obrázek'),
                    'name' => 'current_image_display',
                    'html_content' => '<img src="' . $image_url . '" alt="Aktuální obrázek" style="max-width: 200px; max-height: 200px;" />' .
                                     '<input type="hidden" name="existing_image" value="' . $katalog->image . '" />'
                ];
                array_splice($this->fields_form['input'], 2, 0, [$current_image_input]);
            }

            if ($katalog->file_path) {
                $file_url = _MODULE_DIR_ . 'katalogy/files/' . $katalog->file_path;
                $current_file_input = [
                    'type' => 'html',
                    'label' => $this->l('Aktuální soubor'),
                    'name' => 'current_file_display',
                    'html_content' => '<a href="' . $file_url . '" target="_blank" class="btn btn-default">' . 
                                     $this->l('Stáhnout aktuální soubor') . '</a>' .
                                     '<input type="hidden" name="existing_file_path" value="' . $katalog->file_path . '" />'
                ];
                array_splice($this->fields_form['input'], 5, 0, [$current_file_input]);
            }
        }
        return parent::renderForm();
    }

    public function postProcess()
    {
        if (Tools::isSubmit('submit' . $this->table)) {
            $id = (int)Tools::getValue('id_katalog');
            if ($id > 0) {
                return $this->processUpdate();
            } else {
                return $this->processAdd();
            }
        }
        return parent::postProcess();
    }

    public function processAdd()
    {
        $katalog = new Katalog();
        $this->copyFromPost($katalog, $this->table);
        
        if (empty($katalog->position) || $katalog->position == 0) {
            $katalog->position = $this->getNextPosition();
        }

        $katalog->date_add = date('Y-m-d H:i:s');
        $katalog->date_upd = date('Y-m-d H:i:s');

        if ($katalog->save()) {
            $this->handleFileUploads($katalog);
            $this->confirmations[] = $this->l('Katalog byl úspěšně přidán.');
            $this->redirect_after = self::$currentIndex . '&token=' . $this->token;
        } else {
            $this->errors[] = $this->l('Chyba při ukládání katalogu.');
        }
    }

    public function processUpdate()
    {
        $id = (int)Tools::getValue('id_katalog');
        $katalog = new Katalog($id);

        if (!Validate::isLoadedObject($katalog)) {
            $this->errors[] = $this->l('Katalog nebyl nalezen.');
            return false;
        }

        $original_image = $katalog->image;
        $original_file_path = $katalog->file_path;
        $original_position = $katalog->position;

        $this->copyFromPost($katalog, $this->table);
        $katalog->id = $id;
        $katalog->id_katalog = $id;

        if (empty($_FILES['image']['tmp_name']) && Tools::getValue('existing_image')) {
            $katalog->image = Tools::getValue('existing_image');
        }

        if (empty($_FILES['catalog_file']['tmp_name']) && Tools::getValue('existing_file_path')) {
            $katalog->file_path = Tools::getValue('existing_file_path');
        }

        if (empty($katalog->position) || $katalog->position == 0) {
            $katalog->position = $original_position;
        }

        $katalog->date_upd = date('Y-m-d H:i:s');

        if ($katalog->update()) {
            $this->handleFileUploads($katalog, $original_image, $original_file_path);
            $this->confirmations[] = $this->l('Katalog byl úspěšně upraven.');
            $this->redirect_after = self::$currentIndex . '&token=' . $this->token;
        } else {
            $this->errors[] = $this->l('Chyba při ukládání katalogu.');
        }
    }

    private function handleFileUploads($katalog, $original_image = null, $original_file_path = null)
    {
        $updated = false;

        if (isset($_FILES['image']) && $_FILES['image']['size'] > 0 && $_FILES['image']['error'] == 0) {
            $image_name = $katalog->id . '_' . time() . '.jpg';
            $upload_dir = _PS_MODULE_DIR_ . 'katalogy/views/img/katalogy/';

            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image_name)) {
                if ($original_image && $original_image != $image_name && file_exists($upload_dir . $original_image)) {
                    unlink($upload_dir . $original_image);
                }
                $katalog->image = $image_name;
                $updated = true;
            }
        }

        if (isset($_FILES['catalog_file']) && $_FILES['catalog_file']['size'] > 0 && $_FILES['catalog_file']['error'] == 0) {
            $file_extension = pathinfo($_FILES['catalog_file']['name'], PATHINFO_EXTENSION);
            $file_name = $katalog->id . '_catalog_' . time() . '.' . $file_extension;
            $upload_dir = _PS_MODULE_DIR_ . 'katalogy/files/';

            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            if (move_uploaded_file($_FILES['catalog_file']['tmp_name'], $upload_dir . $file_name)) {
                if ($original_file_path && $original_file_path != $file_name && file_exists($upload_dir . $original_file_path)) {
                    unlink($upload_dir . $original_file_path);
                }
                $katalog->file_path = $file_name;
                $updated = true;
            }
        }

        if ($updated) {
            $katalog->update();
        }
    }

    private function getNextPosition()
    {
        $sql = 'SELECT MAX(`position`) as max_pos FROM `' . _DB_PREFIX_ . 'katalogy`';
        $result = Db::getInstance()->getRow($sql);
        return (int)$result['max_pos'] + 1;
    }



    /**
     * ⭐ FORCE position nastavení
     */
    public function init()
    {
        // FORCE pozice
        $this->_orderBy = 'position';
        $this->_orderWay = 'ASC';

        parent::init();
    }

    /**
     * ⭐ KLÍČOVÁ METODA: Načítání JavaScript a CSS pro drag & drop
     */
    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);

        // Přidání jQuery UI pro drag & drop (pokud není načteno)
        $this->addJqueryUI('ui.sortable');

        // Přidání našeho JavaScript souboru pro drag & drop
        $this->addJS(_MODULE_DIR_ . 'katalogy/views/js/admin-drag-drop.js');

        // Přidání CSS pro lepší vzhled drag & drop
        $this->addCSS(_MODULE_DIR_ . 'katalogy/views/css/admin-katalogy.css');
    }

    /**
     * ⭐ ALTERNATIVNÍ METODA: Pokud pozice nefungují automaticky
     */
    public function ajaxProcessPosition()
    {
        $way = (int)Tools::getValue('way');
        $id = (int)Tools::getValue($this->identifier);

        if (!$id) {
            die(json_encode(['hasError' => true, 'errors' => ['Invalid ID']]));
        }

        $katalog = new Katalog($id);
        if (!Validate::isLoadedObject($katalog)) {
            die(json_encode(['hasError' => true, 'errors' => ['Katalog not found']]));
        }

        $current_position = (int)$katalog->position;
        $new_position = $way ? max(1, $current_position - 1) : $current_position + 1;

        // Najdi katalog na cílové pozici
        $sql = 'SELECT id_katalog FROM `' . _DB_PREFIX_ . 'katalogy` WHERE position = ' . (int)$new_position;
        $target_id = Db::getInstance()->getValue($sql);

        if ($target_id) {
            // Prohoď pozice
            Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'katalogy` SET position = ' . (int)$current_position . ' WHERE id_katalog = ' . (int)$target_id);
        }

        // Aktualizuj pozici aktuálního katalogu
        $katalog->position = $new_position;
        if ($katalog->update()) {
            die(json_encode(['hasError' => false, 'updated' => 1]));
        } else {
            die(json_encode(['hasError' => true, 'errors' => ['Update failed']]));
        }
    }

    }