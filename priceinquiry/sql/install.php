<?php
/**
 * SQL skript pro vytvoření tabulky dotazů na cenu
 */

$sql = array();

// Nejdříve zkusíme smazat tabulku pokud existuje (pro čistou instalaci)
$sql[] = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'price_inquiry`';

// Vytvoříme novou tabulku s kompletní strukturou
$sql[] = 'CREATE TABLE `'._DB_PREFIX_.'price_inquiry` (
    `id_price_inquiry` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_product` int(10) unsigned NOT NULL,
    `id_product_attribute` int(10) unsigned DEFAULT 0,
    `id_customer` int(10) unsigned DEFAULT 0,
    `customer_name` varchar(255) NOT NULL,
    `customer_email` varchar(255) NOT NULL,
    `customer_phone` varchar(50) DEFAULT NULL,
    `customer_company` varchar(255) DEFAULT NULL,
    `quantity` int(10) unsigned DEFAULT 1,
    `product_name` varchar(255) NOT NULL,
    `product_reference` varchar(64) DEFAULT NULL,
    `message` text DEFAULT NULL,
    `date_add` datetime NOT NULL,
    `resolved` tinyint(1) NOT NULL DEFAULT 0,
    `date_resolved` datetime DEFAULT NULL,
    PRIMARY KEY (`id_price_inquiry`),
    KEY `id_product` (`id_product`),
    KEY `id_customer` (`id_customer`),
    KEY `resolved` (`resolved`),
    KEY `date_add` (`date_add`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

// Spustíme SQL příkazy

foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}