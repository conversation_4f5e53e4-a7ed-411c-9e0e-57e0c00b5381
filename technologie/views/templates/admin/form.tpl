{**
 * Admin šablona pro formulář technologie - OPRAVENÁ VERZE BEZ SMARTY KONFLIKTŮ
 * Problém: JavaScript template literals kolidují se Smarty tagy
 *}

<div class="panel technologie-admin">
    <div class="panel-heading">
        <i class="icon-{if $is_edit}edit{else}plus{/if}"></i>
        {if $is_edit}
            {l s='Upravit technologii' mod='technologie'}: {if isset($technologie) && $technologie && isset($technologie.name) && $technologie.name}{$technologie.name|escape:'html':'UTF-8'}{/if}
        {else}
            {l s='Přidat novou technologii' mod='technologie'}
        {/if}
    </div>

    <div class="panel-body">
        {if isset($errors) && is_array($errors) && count($errors) > 0}
            <div class="alert alert-danger">
                <ul class="mb-0">
                    {foreach from=$errors item=error}
                        <li>{$error|escape:'html':'UTF-8'}</li>
                    {/foreach}
                </ul>
            </div>
        {/if}



        {* FORMULÁŘ *}
        <form action="{$smarty.server.REQUEST_URI|escape:'html':'UTF-8'}" 
              method="post" 
              enctype="multipart/form-data" 
              class="form-horizontal"
              id="technologie-form">

            {* Název technologie *}
            <div class="form-group">
                <label class="control-label col-lg-3 required">
                    {l s='Název technologie' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    <input type="text"
                           name="name"
                           value="{if isset($technologie) && $technologie && isset($technologie.name)}{$technologie.name|escape:'html':'UTF-8'}{/if}"
                           class="form-control"
                           placeholder="{l s='Zadejte název technologie' mod='technologie'}"
                           maxlength="255"
                           required />
                    <p class="help-block">{l s='Povinné pole. Maximálně 255 znaků.' mod='technologie'}</p>
                </div>
            </div>

            {* Popis technologie *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Popis technologie' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    <textarea name="description"
                              class="form-control"
                              rows="4"
                              placeholder="{l s='Zadejte popis technologie' mod='technologie'}"
                              maxlength="1000">{if isset($technologie) && $technologie && isset($technologie.description)}{$technologie.description|escape:'html':'UTF-8'}{/if}</textarea>
                    <p class="help-block">{l s='Nepovinné pole. Maximálně 1000 znaků.' mod='technologie'}</p>
                </div>
            </div>

            {* Obrázek *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Obrázek technologie' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    {if $is_edit && isset($technologie) && $technologie && isset($technologie.image) && $technologie.image && isset($technologie.image_url) && $technologie.image_url}
                        <div class="current-image mb-3">
                            <p><strong>{l s='Aktuální obrázek:' mod='technologie'}</strong></p>
                            <img src="{$technologie.image_url|escape:'html':'UTF-8'}"
                                 alt="{if isset($technologie.name)}{$technologie.name|escape:'html':'UTF-8'}{else}Obrázek technologie{/if}"
                                 class="img-thumbnail current-tech-image"
                                 style="max-width: 200px; max-height: 200px;" />
                            <p class="text-muted mt-2">
                                <small>{l s='Nahrajte nový obrázek pro nahrazení současného' mod='technologie'}</small>
                            </p>
                        </div>
                    {/if}
                    
                    <input type="file"
                           name="image"
                           class="form-control"
                           accept="image/*"
                           id="technologie-image-input" />
                    <p class="help-block">
                        {l s='Podporované formáty: JPG, PNG, GIF, WebP. Maximální velikost: 2MB.' mod='technologie'}
                    </p>
                    
                    {* Live upload info *}
                    <div id="upload-info" style="margin-top: 10px; padding: 10px; background: #e8f4f8; border: 1px solid #bee5eb; border-radius: 4px; display: none;">
                        <strong>📁 Informace o souboru:</strong><br>
                        <span id="file-info"></span>
                    </div>
                </div>
            </div>

            {* URL Slug *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='URL slug' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    <input type="text"
                           name="slug"
                           value="{if isset($technologie) && $technologie && isset($technologie.slug)}{$technologie.slug|escape:'html':'UTF-8'}{/if}"
                           class="form-control"
                           id="technologie-slug"
                           maxlength="255"
                           placeholder="{l s='Automaticky generováno z názvu' mod='technologie'}" />
                    <p class="help-block">
                        {l s='SEO-friendly URL adresa pro detail stránku. Pokud nevyplníte, bude automaticky generována z názvu.' mod='technologie'}
                        <br><small>{l s='Povolené znaky: malá písmena, číslice, pomlčky' mod='technologie'}</small>
                    </p>
                </div>
            </div>

            {* Detailní popis *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Detailní popis' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    <textarea name="detailed_description"
                              class="form-control wysiwyg-editor"
                              id="technologie-detailed-description"
                              rows="8"
                              placeholder="{l s='Zadejte podrobný popis technologie pro detail stránku' mod='technologie'}">{if isset($technologie) && $technologie && isset($technologie.detailed_description)}{$technologie.detailed_description|escape:'html':'UTF-8'}{/if}</textarea>
                    <p class="help-block">{l s='Podrobný popis se zobrazí na detail stránce technologie. Můžete použít HTML tagy.' mod='technologie'}</p>
                </div>
            </div>

            {* Výhody technologie *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Výhody technologie' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    <textarea name="advantages"
                              class="form-control"
                              id="technologie-advantages"
                              rows="6"
                              placeholder="{l s='Zadejte výhody, každou na nový řádek' mod='technologie'}">{if isset($technologie) && $technologie && isset($technologie.advantages)}{$technologie.advantages|escape:'html':'UTF-8'}{/if}</textarea>
                    <p class="help-block">
                        {l s='Zadejte výhody technologie, každou na nový řádek. Zobrazí se jako seznam s checkmarky.' mod='technologie'}
                        <br><small>{l s='Příklad:' mod='technologie'}<br>
                        {l s='Vysoká kvalita tisku' mod='technologie'}<br>
                        {l s='Rychlé zpracování' mod='technologie'}<br>
                        {l s='Dostupné ceny' mod='technologie'}</small>
                    </p>
                </div>
            </div>

            {* Oblasti použití *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Oblasti použití' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    <textarea name="applications"
                              class="form-control"
                              id="technologie-applications"
                              rows="6"
                              placeholder="{l s='Zadejte oblasti použití, každou na nový řádek' mod='technologie'}">{if isset($technologie) && $technologie && isset($technologie.applications)}{$technologie.applications|escape:'html':'UTF-8'}{/if}</textarea>
                    <p class="help-block">
                        {l s='Zadejte oblasti použití technologie, každou na nový řádek.' mod='technologie'}
                        <br><small>{l s='Příklad:' mod='technologie'}<br>
                        {l s='Reklamní předměty' mod='technologie'}<br>
                        {l s='Firemní oblečení' mod='technologie'}<br>
                        {l s='Propagační materiály' mod='technologie'}</small>
                    </p>
                </div>
            </div>

            {* Galerie obrázků *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Galerie realizací' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    {* Současné obrázky galerie *}
                    {if isset($technologie) && $technologie && isset($technologie.gallery_images) && $technologie.gallery_images}
                        <div class="current-gallery mb-3">
                            <p><strong>{l s='Současné obrázky v galerii:' mod='technologie'}</strong></p>
                            <div class="gallery-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 10px; margin-bottom: 15px;">
                                {foreach from=$technologie.gallery_images_array item=image}
                                    <div class="gallery-item" style="position: relative;">
                                        <img src="{$upload_dir|escape:'html':'UTF-8'}gallery/{$image|escape:'html':'UTF-8'}"
                                             alt="Galerie obrázek"
                                             class="img-thumbnail"
                                             style="width: 100%; height: 120px; object-fit: cover;" />
                                        <button type="button"
                                                class="btn btn-danger btn-xs remove-gallery-image"
                                                data-image="{$image|escape:'html':'UTF-8'}"
                                                style="position: absolute; top: 5px; right: 5px; padding: 2px 6px;">
                                            <i class="icon-trash"></i>
                                        </button>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    {/if}

                    {* Upload nových obrázků *}
                    <div class="gallery-upload">
                        <input type="file"
                               name="gallery_images[]"
                               class="form-control gallery-image-input"
                               accept="image/*"
                               multiple
                               id="technologie-gallery-input" />
                        <p class="help-block">
                            {l s='Vyberte více obrázků najednou pro galerii realizací. Podporované formáty: JPG, PNG, GIF, WebP. Maximální velikost: 2MB na obrázek.' mod='technologie'}
                        </p>

                        {* Preview nových obrázků *}
                        <div id="gallery-preview" style="margin-top: 15px; display: none;">
                            <p><strong>{l s='Náhled nových obrázků:' mod='technologie'}</strong></p>
                            <div id="gallery-preview-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 10px;"></div>
                        </div>
                    </div>

                    {* Hidden input pro smazané obrázky *}
                    <input type="hidden" name="removed_gallery_images" id="removed-gallery-images" value="" />
                </div>
            </div>

            {* Pořadí *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Pořadí' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    <input type="number"
                           name="position"
                           value="{if isset($technologie) && $technologie && isset($technologie.position) && $technologie.position > 0}{$technologie.position}{/if}"
                           class="form-control"
                           min="0"
                           placeholder="{l s='Zadejte pořadí (nechte prázdné pro automatické)' mod='technologie'}" />
                    <p class="help-block">{l s='Čím nižší číslo, tím výše se technologie zobrazí. Pokud nevyplníte, bude automaticky přiřazeno.' mod='technologie'}</p>
                </div>
            </div>

            {* Aktivní *}
            <div class="form-group">
                <label class="control-label col-lg-3">
                    {l s='Stav' mod='technologie'}
                </label>
                <div class="col-lg-9">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox"
                                   name="active"
                                   value="1"
                                   {if !isset($technologie) || !$technologie || !isset($technologie.active) || $technologie.active == 1}checked{/if} />
                            {l s='Aktivní (zobrazí se na webu)' mod='technologie'}
                        </label>
                    </div>
                    <p class="help-block">{l s='Pouze aktivní technologie se zobrazují návštěvníkům webu.' mod='technologie'}</p>
                </div>
            </div>

            {* Tlačítka *}
            <div class="form-group">
                <div class="col-lg-9 col-lg-offset-3">
                    <button type="submit" name="submitAddtechnologie" value="1"
                            class="btn btn-primary btn-technologie"
                            id="submit-btn">
                        <i class="icon-save"></i>
                        {if $is_edit}
                            {l s='Aktualizovat technologii' mod='technologie'}
                        {else}
                            {l s='Přidat technologii' mod='technologie'}
                        {/if}
                    </button>
                    
                    <a href="{$back_url|escape:'html':'UTF-8'}" class="btn btn-default">
                        <i class="icon-arrow-left"></i>
                        {l s='Zpět na seznam' mod='technologie'}
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

{* JavaScript s opravou Smarty konfliktů *}
<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    var form = document.getElementById('technologie-form');
    var imageInput = document.getElementById('technologie-image-input');
    var submitBtn = document.getElementById('submit-btn');
    var uploadInfo = document.getElementById('upload-info');
    var fileInfo = document.getElementById('file-info');

    // Nové prvky pro rozšířenou funkcionalitu
    var nameInput = document.querySelector('input[name="name"]');
    var slugInput = document.getElementById('technologie-slug');
    var galleryInput = document.getElementById('technologie-gallery-input');
    var galleryPreview = document.getElementById('gallery-preview');
    var galleryPreviewGrid = document.getElementById('gallery-preview-grid');
    var removedImagesInput = document.getElementById('removed-gallery-images');

    // Enhanced image handling
    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            var file = e.target.files[0];
            
            if (file) {
                // Zobrazení informací o souboru
                fileInfo.innerHTML =
                    '<strong>Název:</strong> ' + file.name + '<br>' +
                    '<strong>Typ:</strong> ' + file.type + '<br>' +
                    '<strong>Velikost:</strong> ' + (file.size / 1024).toFixed(1) + ' KB<br>' +
                    '<strong>Poslední změna:</strong> ' + new Date(file.lastModified).toLocaleString();
                uploadInfo.style.display = 'block';

                // Validace typu
                if (!file.type.match('image.*')) {
                    alert('{l s="Vyberte prosím obrázek" mod="technologie" js=1}');
                    this.value = '';
                    uploadInfo.style.display = 'none';
                    return;
                }

                // Validace velikosti (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert('{l s="Obrázek je příliš velký. Maximální velikost je 2MB" mod="technologie" js=1}');
                    this.value = '';
                    uploadInfo.style.display = 'none';
                    return;
                }

                // Preview
                var reader = new FileReader();
                reader.onload = function(e) {
                    // Odstranění starého preview
                    var oldPreview = document.querySelector('.image-preview');
                    if (oldPreview) {
                        oldPreview.remove();
                    }
                    
                    // Vytvoření nového preview
                    var preview = document.createElement('div');
                    preview.className = 'image-preview mt-3';
                    preview.innerHTML = 
                        '<p><strong>{l s="Náhled nového obrázku:" mod="technologie" js=1}</strong></p>' +
                        '<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />';
                    
                    imageInput.parentNode.appendChild(preview);
                };
                reader.readAsDataURL(file);
            } else {
                uploadInfo.style.display = 'none';
            }
        });
    }

    // Automatické generování slug z názvu
    if (nameInput && slugInput) {
        nameInput.addEventListener('input', function() {
            // Generuj slug pouze pokud je pole prázdné nebo obsahuje automaticky generovaný slug
            if (!slugInput.value || slugInput.dataset.autoGenerated === 'true') {
                var slug = generateSlug(this.value);
                slugInput.value = slug;
                slugInput.dataset.autoGenerated = 'true';
            }
        });

        // Pokud uživatel ručně upraví slug, označíme ho jako manuální
        slugInput.addEventListener('input', function() {
            this.dataset.autoGenerated = 'false';
        });
    }

    // Funkce pro generování slug
    function generateSlug(text) {
        return text
            .toLowerCase()
            .replace(/[áàâäã]/g, 'a')
            .replace(/[éèêë]/g, 'e')
            .replace(/[íìîï]/g, 'i')
            .replace(/[óòôöõ]/g, 'o')
            .replace(/[úùûü]/g, 'u')
            .replace(/[ýÿ]/g, 'y')
            .replace(/[ñ]/g, 'n')
            .replace(/[ç]/g, 'c')
            .replace(/[ř]/g, 'r')
            .replace(/[š]/g, 's')
            .replace(/[č]/g, 'c')
            .replace(/[ť]/g, 't')
            .replace(/[ž]/g, 'z')
            .replace(/[ď]/g, 'd')
            .replace(/[ň]/g, 'n')
            .replace(/[ů]/g, 'u')
            .replace(/[ě]/g, 'e')
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }

    // Zpracování galerie obrázků
    if (galleryInput) {
        galleryInput.addEventListener('change', function(e) {
            var files = Array.from(e.target.files);
            galleryPreviewGrid.innerHTML = '';

            if (files.length > 0) {
                galleryPreview.style.display = 'block';

                files.forEach(function(file, index) {
                    // Validace souboru
                    if (!file.type.match('image.*')) {
                        alert('{l s="Soubor" mod="technologie" js=1} ' + file.name + ' {l s="není obrázek" mod="technologie" js=1}');
                        return;
                    }

                    if (file.size > 2 * 1024 * 1024) {
                        alert('{l s="Obrázek" mod="technologie" js=1} ' + file.name + ' {l s="je příliš velký (max 2MB)" mod="technologie" js=1}');
                        return;
                    }

                    // Vytvoření preview
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var previewItem = document.createElement('div');
                        previewItem.className = 'gallery-preview-item';
                        previewItem.style.cssText = 'position: relative; border: 2px solid #ddd; border-radius: 4px; overflow: hidden;';

                        previewItem.innerHTML =
                            '<img src="' + e.target.result + '" style="width: 100%; height: 120px; object-fit: cover;" />' +
                            '<div style="position: absolute; bottom: 0; left: 0; right: 0; background: rgba(0,0,0,0.7); color: white; padding: 5px; font-size: 11px;">' +
                                '<div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">' + file.name + '</div>' +
                                '<div>' + (file.size / 1024).toFixed(1) + ' KB</div>' +
                            '</div>';

                        galleryPreviewGrid.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                });
            } else {
                galleryPreview.style.display = 'none';
            }
        });
    }

    // Zpracování mazání obrázků z galerie
    var removedImages = [];
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-gallery-image') || e.target.parentNode.classList.contains('remove-gallery-image')) {
            e.preventDefault();
            var button = e.target.classList.contains('remove-gallery-image') ? e.target : e.target.parentNode;
            var imageName = button.dataset.image;

            if (confirm('{l s="Opravdu chcete smazat tento obrázek z galerie?" mod="technologie" js=1}')) {
                // Přidáme do seznamu smazaných
                removedImages.push(imageName);
                removedImagesInput.value = removedImages.join(',');

                // Skryjeme obrázek
                button.closest('.gallery-item').style.display = 'none';
            }
        }
    });

    // Zpracování odeslání formuláře
    if (form) {
        form.addEventListener('submit', function(e) {
            // Validace názvu
            if (!nameInput.value.trim()) {
                alert('{l s="Název technologie je povinný" mod="technologie" js=1}');
                e.preventDefault();
                nameInput.focus();
                return false;
            }

            // Validace slug (pokud je vyplněn)
            if (slugInput.value) {
                var slugPattern = /^[a-z0-9\-]*$/;
                if (!slugPattern.test(slugInput.value)) {
                    alert('{l s="Slug může obsahovat pouze malá písmena, číslice a pomlčky" mod="technologie" js=1}');
                    e.preventDefault();
                    slugInput.focus();
                    return false;
                }
            }

            // Změna tlačítka během ukládání
            submitBtn.innerHTML = '<i class="icon-spinner icon-spin"></i> Ukládání...';
            submitBtn.disabled = true;
        });
    }
});
</script>

<style>
.image-preview {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.image-preview img {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.current-tech-image {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-technologie {
    background-color: #007cff;
    border-color: #007cff;
    color: white;
    transition: all 0.2s ease;
}

.btn-technologie:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
}



#upload-info {
    font-size: 13px;
}

/* Styly pro nová pole */
.wysiwyg-editor {
    min-height: 200px;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.gallery-item {
    position: relative;
    border: 2px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    transition: border-color 0.2s ease;
}

.gallery-item:hover {
    border-color: #007cff;
}

.remove-gallery-image {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1;
    border-radius: 2px;
    background: rgba(220, 53, 69, 0.9);
    border: none;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.remove-gallery-image:hover {
    background: rgba(220, 53, 69, 1);
}

.gallery-upload {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.2s ease;
}

.gallery-upload:hover {
    border-color: #007cff;
}

.gallery-preview-item {
    position: relative;
    border: 2px solid #28a745;
    border-radius: 4px;
    overflow: hidden;
}

#technologie-slug {
    font-family: monospace;
}

#technologie-slug[data-auto-generated="true"] {
    background-color: #f8f9fa;
    font-style: italic;
}

.help-block small {
    color: #6c757d;
    font-style: italic;
}
</style>