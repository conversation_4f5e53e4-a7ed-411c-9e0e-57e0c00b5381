<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> test tabů - bez složitých kontrol přístupu
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

echo "<h1>Rychlý test tabů</h1>";

// Test 1: Existence CustomModulesTabManager
echo "<h2>1. Test CustomModulesTabManager</h2>";
require_once(dirname(__FILE__) . '/classes/CustomModulesTabManager.php');

if (class_exists('CustomModulesTabManager')) {
    echo "<p style='color: green;'>✅ CustomModulesTabManager načten</p>";
} else {
    echo "<p style='color: red;'>❌ CustomModulesTabManager nenačten</p>";
    exit;
}

// Test 2: Existence sekce
echo "<h2>2. Test existence sekce</h2>";
$exists = CustomModulesTabManager::customModulesSectionExists();
echo "<p>" . ($exists ? "✅ Sekce existuje" : "❌ Sekce neexistuje") . "</p>";

if ($exists) {
    $custom_id = Tab::getIdFromClassName('AdminCustomModules');
    echo "<p>ID sekce: $custom_id</p>";
}

// Test 3: Pozice tabů
echo "<h2>3. Test pozic</h2>";
$sql = 'SELECT class_name, position FROM `' . _DB_PREFIX_ . 'tab` 
        WHERE id_parent = 0 AND active = 1 
        AND (class_name = "AdminParentPreferences" OR class_name = "AdminCustomModules")
        ORDER BY position';

$tabs = Db::getInstance()->executeS($sql);
foreach ($tabs as $tab) {
    $name = $tab['class_name'] == 'AdminParentPreferences' ? 'KONFIGURACE' : 'VLASTNÍ MODULY';
    echo "<p><strong>$name:</strong> pozice {$tab['position']}</p>";
}

// Test 4: Moduly v sekci
if ($exists) {
    echo "<h2>4. Moduly v sekci</h2>";
    $modules = CustomModulesTabManager::getCustomModulesTabs();
    if (!empty($modules)) {
        foreach ($modules as $module) {
            echo "<p>📄 {$module['name']} ({$module['class_name']})</p>";
        }
    } else {
        echo "<p>Žádné moduly v sekci</p>";
    }
}

// Akce
echo "<h2>Akce</h2>";

if (!$exists) {
    echo "<p><a href='?action=create' style='background: #4caf50; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>➕ Vytvořit sekci</a></p>";
} else {
    echo "<p><a href='?action=fix_position' style='background: #ff9800; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🔧 Opravit pozici</a></p>";
    echo "<p><a href='?action=remove' style='background: #f44336; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🗑️ Odstranit sekci</a></p>";
}

// Zpracování akcí
if (isset($_GET['action'])) {
    echo "<hr><h2>Výsledek akce:</h2>";
    
    switch ($_GET['action']) {
        case 'create':
            $result = CustomModulesTabManager::createOrGetCustomModulesTab();
            if ($result) {
                echo "<p style='color: green;'>✅ Sekce vytvořena (ID: $result)</p>";
            } else {
                echo "<p style='color: red;'>❌ Chyba při vytváření sekce</p>";
            }
            break;
            
        case 'fix_position':
            // Najdi pozice
            $config_pos = null;
            $custom_pos = null;
            
            foreach ($tabs as $tab) {
                if ($tab['class_name'] == 'AdminParentPreferences') $config_pos = $tab['position'];
                if ($tab['class_name'] == 'AdminCustomModules') $custom_pos = $tab['position'];
            }
            
            if ($config_pos !== null && $custom_pos !== null) {
                $target = $config_pos + 1;
                
                // Přesuň taby
                $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                        SET position = position + 1 
                        WHERE position >= ' . (int)$target . ' AND id_parent = 0 
                        AND class_name != "AdminCustomModules"';
                Db::getInstance()->execute($sql);
                
                // Nastav pozici
                $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                        SET position = ' . (int)$target . ' 
                        WHERE class_name = "AdminCustomModules"';
                
                if (Db::getInstance()->execute($sql)) {
                    echo "<p style='color: green;'>✅ Pozice opravena na $target</p>";
                } else {
                    echo "<p style='color: red;'>❌ Chyba při opravě pozice</p>";
                }
            }
            break;
            
        case 'remove':
            CustomModulesTabManager::cleanupCustomModulesTabIfEmpty();
            echo "<p style='color: green;'>✅ Pokus o odstranění dokončen</p>";
            break;
    }
    
    echo "<p><a href='?'>🔄 Obnovit test</a></p>";
}

echo "<hr>";
echo "<p><a href='debug-tab-positions.php'>🔍 Detailní debug</a> | <a href='manage-custom-tabs.php'>🔧 Správa</a></p>";
?>
