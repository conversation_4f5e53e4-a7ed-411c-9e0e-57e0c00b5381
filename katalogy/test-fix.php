<?php
/**
 * Test script pro ověření oprav modulu katalogy
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

echo "<h1>Test oprav modulu Katalogy</h1>";

// Kontrola existence modulu
$module = Module::getInstanceByName('katalogy');
if (!$module) {
    echo "<p style='color: red;'>❌ Modul katalogy není nainstalován</p>";
    exit;
}

echo "<p style='color: green;'>✅ Modul katalogy je nainstalován</p>";

// Kontrola registrovaných hooků
$hooks_to_check = [
    'displayHeader',
    'actionFrontControllerSetMedia', 
    'displayKatalogyContent',
    'displayKatalogySimple',
    'displayCMSContent',
    'displayRightColumn',
    'displayLeftColumn',
    'displayTop',
    'displayBeforeBodyClosingTag'
];

echo "<h2>Registrované hooky:</h2>";
foreach ($hooks_to_check as $hook_name) {
    $hook_id = Hook::getIdByName($hook_name);
    if ($hook_id) {
        $sql = "SELECT * FROM `" . _DB_PREFIX_ . "hook_module` WHERE id_hook = $hook_id AND id_module = " . (int)$module->id;
        $hook_module = Db::getInstance()->getRow($sql);
        if ($hook_module) {
            echo "<p style='color: green;'>✅ Hook '$hook_name' je registrován</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Hook '$hook_name' není registrován</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Hook '$hook_name' neexistuje</p>";
    }
}

// Kontrola konfigurace
echo "<h2>Konfigurace:</h2>";
$email = Configuration::get('KATALOGY_EMAIL');
$cms_id = Configuration::get('KATALOGY_CMS_ID');

echo "<p>Email: " . ($email ? "<span style='color: green;'>$email</span>" : "<span style='color: red;'>Není nastaven</span>") . "</p>";
echo "<p>CMS ID: " . ($cms_id ? "<span style='color: green;'>$cms_id</span>" : "<span style='color: orange;'>Automatická detekce</span>") . "</p>";

// Kontrola CMS stránek s katalogy
echo "<h2>CMS stránky s katalogy:</h2>";
$cms_pages = CMS::getCMSPages(Context::getContext()->language->id);
foreach ($cms_pages as $page) {
    $cms = new CMS($page['id_cms'], Context::getContext()->language->id);
    if (Validate::isLoadedObject($cms)) {
        $has_shortcode = strpos($cms->content, '[katalogy]') !== false || strpos($cms->content, '[katalogy-simple]') !== false;
        $is_catalog_page = strpos($cms->link_rewrite, 'katalog') !== false || 
                          strpos($cms->link_rewrite, 'catalog') !== false ||
                          strpos(strtolower($cms->meta_title), 'katalog') !== false;
        
        if ($has_shortcode || $is_catalog_page) {
            echo "<p>";
            echo "<strong>" . $cms->meta_title . "</strong> (ID: " . $cms->id . ")";
            if ($has_shortcode) {
                echo " <span style='color: blue;'>📝 Obsahuje shortcode</span>";
            }
            if ($is_catalog_page) {
                echo " <span style='color: green;'>🎯 Katalogová stránka</span>";
            }
            echo "</p>";
        }
    }
}

// Test metod modulu
echo "<h2>Test metod modulu:</h2>";
try {
    // Test isCatalogPage
    if (method_exists($module, 'isCatalogPage')) {
        echo "<p style='color: green;'>✅ Metoda isCatalogPage existuje</p>";
    } else {
        echo "<p style='color: red;'>❌ Metoda isCatalogPage neexistuje</p>";
    }
    
    // Test renderKatalogyContent
    if (method_exists($module, 'renderKatalogyContent')) {
        echo "<p style='color: green;'>✅ Metoda renderKatalogyContent existuje</p>";
    } else {
        echo "<p style='color: red;'>❌ Metoda renderKatalogyContent neexistuje</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Chyba při testování metod: " . $e->getMessage() . "</p>";
}

echo "<h2>Doporučení:</h2>";
echo "<ul>";
echo "<li>Zkontrolujte, že na stránce katalogů se obsah zobrazuje pouze jednou</li>";
echo "<li>Ověřte, že shortcode [katalogy] a [katalogy-simple] fungují správně</li>";
echo "<li>Zkontrolujte, že se obsah nezobrazuje v levém/pravém sloupci duplicitně</li>";
echo "<li>Otestujte formulář pro zájem o katalog</li>";
echo "</ul>";

echo "<p><a href='../'>← Zpět na seznam modulů</a></p>";
?>
