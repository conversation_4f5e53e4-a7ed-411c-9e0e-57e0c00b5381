<?php
/**
 * AJAX endpoint for getting calendar data
 * CIG Realizace - Phase 05
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check
require_once '../includes/auth_check.php';

// Check if user is logged in
$current_user = getCurrentUser();
if (!$current_user) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Uživatel nen<PERSON>']);
    exit;
}

// Get and validate input parameters
$month = intval($_GET['month'] ?? date('n'));
$year = intval($_GET['year'] ?? date('Y'));
$view = $_GET['view'] ?? 'month';
$sales_rep = $_GET['sales_rep'] ?? '';
$technology = $_GET['technology'] ?? '';
$search = trim($_GET['search'] ?? '');

// Validate month and year
if ($month < 1 || $month > 12) {
    $month = date('n');
}
if ($year < 2020 || $year > 2030) {
    $year = date('Y');
}

// Validate view type
if (!in_array($view, ['month', 'week', 'day'])) {
    $view = 'month';
}

try {
    $pdo = getDbConnection();
    
    // Calculate date range based on view
    switch ($view) {
        case 'month':
            $start_date = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
            $end_date = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));
            break;
        case 'week':
            // For now, use month range - will be refined later
            $start_date = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
            $end_date = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));
            break;
        case 'day':
            // For now, use month range - will be refined later
            $start_date = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
            $end_date = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));
            break;
        default:
            $start_date = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
            $end_date = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));
    }
    
    // Build WHERE clause for filters
    $where_conditions = [
        "o.preview_status = 'approved'",
        "o.is_completed = 0",
        "o.preview_approved_date IS NOT NULL",
        "o.expected_delivery_date IS NOT NULL"
    ];
    $params = [];
    
    // Date range filter - orders that overlap with the view period
    $where_conditions[] = "(o.preview_approved_date <= ? AND o.expected_delivery_date >= ?)";
    $params[] = $end_date;
    $params[] = $start_date;
    
    // Sales representative filter
    if (!empty($sales_rep)) {
        $where_conditions[] = "o.sales_rep = ?";
        $params[] = $sales_rep;
    }
    
    // Technology filter
    if (!empty($technology)) {
        $where_conditions[] = "EXISTS (
            SELECT 1 FROM order_items oi 
            WHERE oi.order_id = o.id 
            AND oi.is_relevant = 1 
            AND oi.technology_assignment LIKE ?
        )";
        $params[] = "%{$technology}%";
    }
    
    // Search filter
    if (!empty($search)) {
        $where_conditions[] = "o.order_code LIKE ?";
        $params[] = "%{$search}%";
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get orders with items count and technologies
    $sql = "
        SELECT o.id, o.order_code, o.sales_rep, o.sales_rep_name,
               o.preview_approved_date, o.expected_delivery_date,
               o.preview_status, o.status,
               COUNT(oi.id) as items_count,
               GROUP_CONCAT(DISTINCT oi.technology_assignment) as technologies
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.is_relevant = 1
        WHERE {$where_clause}
        GROUP BY o.id
        ORDER BY o.preview_approved_date, o.order_code
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    // Format orders for calendar display
    $formatted_orders = [];
    foreach ($orders as $order) {
        $formatted_orders[] = [
            'id' => $order['id'],
            'order_code' => $order['order_code'],
            'sales_rep' => $order['sales_rep'],
            'sales_rep_name' => $order['sales_rep_name'],
            'start_date' => $order['preview_approved_date'],
            'end_date' => $order['expected_delivery_date'],
            'items_count' => $order['items_count'],
            'technologies' => $order['technologies'],
            'preview_status' => $order['preview_status'],
            'status' => $order['status']
        ];
    }
    
    // Get statistics
    $stats = [
        'total_orders' => count($formatted_orders),
        'overdue_orders' => 0,
        'due_soon_orders' => 0,
        'on_track_orders' => 0
    ];
    
    $today = new DateTime();
    foreach ($formatted_orders as $order) {
        $end_date = new DateTime($order['end_date']);
        $days_until_deadline = $today->diff($end_date)->days;
        $is_overdue = $end_date < $today;
        
        if ($is_overdue) {
            $stats['overdue_orders']++;
        } elseif ($days_until_deadline <= 3) {
            $stats['due_soon_orders']++;
        } else {
            $stats['on_track_orders']++;
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'orders' => $formatted_orders,
            'stats' => $stats,
            'view' => $view,
            'month' => $month,
            'year' => $year,
            'start_date' => $start_date,
            'end_date' => $end_date
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Error getting calendar data: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při načítání kalendářních dat: ' . $e->getMessage()
    ]);
}
?>
