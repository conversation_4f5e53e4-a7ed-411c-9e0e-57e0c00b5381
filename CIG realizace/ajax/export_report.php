<?php
require_once '../includes/session.php';
require_once '../includes/auth_check.php';
require_once '../config/database.php';
require_once '../classes/HistoryLogger.php';

// Check admin access for most reports
if ($current_user['role'] !== 'admin' && $current_user['role'] !== 'obchodnik') {
    http_response_code(403);
    exit('Nemáte oprávnění k exportu reportů');
}

// Get parameters
$format = $_GET['format'] ?? 'csv';
$page = $_GET['page'] ?? 'reports';
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$user_id = $_GET['user_id'] ?? '';
$action_type = $_GET['action_type'] ?? '';

try {
    $pdo = getDbConnection();
    
    // Determine what data to export based on page
    switch ($page) {
        case 'system_activity':
            $data = getSystemActivityData($pdo, $date_from, $date_to, $action_type, $user_id);
            $filename = "system_activity_{$date_from}_to_{$date_to}";
            break;
            
        case 'user_activity':
            $data = getUserActivityData($pdo, $date_from, $date_to, $user_id);
            $filename = "user_activity_{$date_from}_to_{$date_to}";
            break;
            
        case 'reports':
        default:
            $data = getGeneralReportData($pdo, $date_from, $date_to);
            $filename = "general_report_{$date_from}_to_{$date_to}";
            break;
    }
    
    // Export based on format
    switch ($format) {
        case 'csv':
            exportToCSV($data, $filename);
            break;
            
        case 'excel':
            exportToExcel($data, $filename);
            break;
            
        case 'pdf':
            exportToPDF($data, $filename, $page);
            break;
            
        default:
            throw new Exception('Nepodporovaný formát exportu');
    }
    
} catch (Exception $e) {
    error_log("Export error: " . $e->getMessage());
    http_response_code(500);
    exit('Chyba při exportu: ' . $e->getMessage());
}

function getSystemActivityData($pdo, $date_from, $date_to, $action_type = '', $user_id = '') {
    $sql = "
        SELECT oh.created_at, oh.action_type, oh.description, oh.old_value, oh.new_value,
               u.username, u.full_name, u.role, o.order_code
        FROM order_history oh
        LEFT JOIN users u ON oh.user_id = u.id
        LEFT JOIN orders o ON oh.order_id = o.id
        WHERE DATE(oh.created_at) BETWEEN ? AND ?
    ";
    
    $params = [$date_from, $date_to];
    
    if ($action_type) {
        $sql .= " AND oh.action_type = ?";
        $params[] = $action_type;
    }
    
    if ($user_id) {
        $sql .= " AND oh.user_id = ?";
        $params[] = $user_id;
    }
    
    $sql .= " ORDER BY oh.created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    return [
        'headers' => ['Datum', 'Typ akce', 'Popis', 'Stará hodnota', 'Nová hodnota', 'Uživatel', 'Role', 'Objednávka'],
        'data' => $stmt->fetchAll(PDO::FETCH_NUM)
    ];
}

function getUserActivityData($pdo, $date_from, $date_to, $user_id = '') {
    $sql = "
        SELECT u.username, u.full_name, u.role,
               COUNT(oh.id) as total_actions,
               COUNT(CASE WHEN oh.action_type = 'preview_status_change' THEN 1 END) as status_changes,
               COUNT(CASE WHEN oh.action_type = 'delivery_date_change' THEN 1 END) as date_changes,
               COUNT(CASE WHEN oh.action_type = 'technology_assignment' THEN 1 END) as tech_assignments,
               COUNT(DISTINCT oh.order_id) as orders_affected,
               MAX(oh.created_at) as last_activity
        FROM users u
        LEFT JOIN order_history oh ON u.id = oh.user_id 
            AND DATE(oh.created_at) BETWEEN ? AND ?
        WHERE u.is_active = 1
    ";
    
    $params = [$date_from, $date_to];
    
    if ($user_id) {
        $sql .= " AND u.id = ?";
        $params[] = $user_id;
    }
    
    $sql .= " GROUP BY u.id ORDER BY total_actions DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    return [
        'headers' => ['Uživatel', 'Celé jméno', 'Role', 'Celkem akcí', 'Změny stavů', 'Změny termínů', 'Technologie', 'Objednávky', 'Poslední aktivita'],
        'data' => $stmt->fetchAll(PDO::FETCH_NUM)
    ];
}

function getGeneralReportData($pdo, $date_from, $date_to) {
    // Get order statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN preview_status = 'not_created' THEN 1 END) as not_created,
            COUNT(CASE WHEN preview_status = 'sent_to_client' THEN 1 END) as sent_to_client,
            COUNT(CASE WHEN preview_status = 'approved' THEN 1 END) as approved,
            COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed
        FROM orders
    ");
    
    $order_stats = $stmt->fetch(PDO::FETCH_NUM);
    
    // Get activity stats
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_actions,
            COUNT(DISTINCT order_id) as orders_affected,
            COUNT(DISTINCT user_id) as active_users
        FROM order_history 
        WHERE DATE(created_at) BETWEEN ? AND ?
    ");
    
    $stmt->execute([$date_from, $date_to]);
    $activity_stats = $stmt->fetch(PDO::FETCH_NUM);
    
    return [
        'headers' => ['Metrika', 'Hodnota'],
        'data' => [
            ['Celkem objednávek', $order_stats[0]],
            ['Nevytvořené náhledy', $order_stats[1]],
            ['Odeslané klientovi', $order_stats[2]],
            ['Schválené', $order_stats[3]],
            ['Dokončené', $order_stats[4]],
            ['Celkem akcí (' . $date_from . ' - ' . $date_to . ')', $activity_stats[0]],
            ['Upravené objednávky', $activity_stats[1]],
            ['Aktivní uživatelé', $activity_stats[2]]
        ]
    ];
}

function exportToCSV($data, $filename) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Write headers
    fputcsv($output, $data['headers'], ';');
    
    // Write data
    foreach ($data['data'] as $row) {
        fputcsv($output, $row, ';');
    }
    
    fclose($output);
}

function exportToExcel($data, $filename) {
    // Simple Excel export using HTML table format
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    echo '<html>';
    echo '<head>';
    echo '<meta charset="utf-8">';
    echo '<style>';
    echo 'table { border-collapse: collapse; width: 100%; }';
    echo 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }';
    echo 'th { background-color: #f2f2f2; font-weight: bold; }';
    echo '</style>';
    echo '</head>';
    echo '<body>';
    echo '<table>';
    
    // Headers
    echo '<tr>';
    foreach ($data['headers'] as $header) {
        echo '<th>' . htmlspecialchars($header) . '</th>';
    }
    echo '</tr>';
    
    // Data
    foreach ($data['data'] as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            echo '<td>' . htmlspecialchars($cell ?? '') . '</td>';
        }
        echo '</tr>';
    }
    
    echo '</table>';
    echo '</body>';
    echo '</html>';
}

function exportToPDF($data, $filename, $page) {
    // Simple PDF export using HTML to PDF conversion
    // Note: In production, you might want to use a proper PDF library like TCPDF or FPDF
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $filename . '.pdf"');
    
    // For now, we'll create a simple HTML version that browsers can print to PDF
    echo '<!DOCTYPE html>';
    echo '<html>';
    echo '<head>';
    echo '<meta charset="utf-8">';
    echo '<title>' . htmlspecialchars($filename) . '</title>';
    echo '<style>';
    echo 'body { font-family: Arial, sans-serif; margin: 20px; }';
    echo 'h1 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }';
    echo 'table { border-collapse: collapse; width: 100%; margin-top: 20px; }';
    echo 'th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }';
    echo 'th { background-color: #667eea; color: white; font-weight: bold; }';
    echo 'tr:nth-child(even) { background-color: #f9f9f9; }';
    echo '.header { text-align: center; margin-bottom: 30px; }';
    echo '.footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }';
    echo '@media print { body { margin: 0; } }';
    echo '</style>';
    echo '</head>';
    echo '<body>';
    
    echo '<div class="header">';
    echo '<h1>CIG Realizace - Report</h1>';
    echo '<p>Vygenerováno: ' . date('d.m.Y H:i') . '</p>';
    echo '<p>Období: ' . date('d.m.Y', strtotime($_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days')))) . ' - ' . date('d.m.Y', strtotime($_GET['date_to'] ?? date('Y-m-d'))) . '</p>';
    echo '</div>';
    
    echo '<table>';
    
    // Headers
    echo '<tr>';
    foreach ($data['headers'] as $header) {
        echo '<th>' . htmlspecialchars($header) . '</th>';
    }
    echo '</tr>';
    
    // Data
    foreach ($data['data'] as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            echo '<td>' . htmlspecialchars($cell ?? '') . '</td>';
        }
        echo '</tr>';
    }
    
    echo '</table>';
    
    echo '<div class="footer">';
    echo '<p>CIG Realizace - Systém pro správu objednávek</p>';
    echo '</div>';
    
    echo '</body>';
    echo '</html>';
}
?>
