<?php
/**
 * Load Filter AJAX Endpoint
 * CIG Realizace - Phase 09
 * Loads saved filter configurations
 */

header('Content-Type: application/json');

// Include required files
require_once '../includes/auth_check.php';
require_once '../classes/FilterManager.php';

// Check if user is authenticated
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    // Get current user
    $current_user = getCurrentUser();
    $user_id = $current_user['id'];
    
    // Get action
    $action = $_GET['action'] ?? 'get_filters';
    
    // Initialize filter manager
    $filterManager = new FilterManager();
    
    switch ($action) {
        case 'get_filters':
            // Get user's saved filters
            $filters = $filterManager->getUserFilters($user_id);
            echo json_encode($filters);
            break;
            
        case 'get_presets':
            // Get filter presets
            $presets = $filterManager->getFilterPresets();
            echo json_encode($presets);
            break;
            
        case 'load_filter':
            // Load specific filter
            $filter_id = intval($_GET['id'] ?? 0);
            
            if ($filter_id <= 0) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid filter ID']);
                exit;
            }
            
            $filter = $filterManager->loadFilter($user_id, $filter_id);
            
            if ($filter) {
                echo json_encode($filter);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Filter not found']);
            }
            break;
            
        case 'delete_filter':
            // Delete filter (only POST/DELETE allowed)
            if ($_SERVER['REQUEST_METHOD'] !== 'POST' && $_SERVER['REQUEST_METHOD'] !== 'DELETE') {
                http_response_code(405);
                echo json_encode(['error' => 'Method not allowed']);
                exit;
            }
            
            $filter_id = intval($_POST['id'] ?? $_GET['id'] ?? 0);
            
            if ($filter_id <= 0) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid filter ID']);
                exit;
            }
            
            $result = $filterManager->deleteFilter($user_id, $filter_id);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Filter byl úspěšně smazán'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to delete filter']);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    error_log("Load filter error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
