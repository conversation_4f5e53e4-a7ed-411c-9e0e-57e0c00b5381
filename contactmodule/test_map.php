<?php
/**
 * Test soubor pro ověření mapy
 */

require_once(dirname(__FILE__).'/../../config/config.inc.php');

echo "<h2>Test mapy - Contact Module</h2>";

$map_embed = Configuration::get('CONTACT_MAP_EMBED');

echo "<h3>Uložená mapa v databázi:</h3>";
echo "<p><strong>Délka:</strong> " . strlen($map_embed) . " znaků</p>";
echo "<p><strong>Prázdná:</strong> " . (empty($map_embed) ? 'ANO' : 'NE') . "</p>";

if (!empty($map_embed)) {
    echo "<h3>Obsah:</h3>";
    echo "<textarea style='width: 100%; height: 200px;'>" . htmlspecialchars($map_embed) . "</textarea>";
    
    echo "<h3>Zobrazení:</h3>";
    echo "<div style='border: 1px solid #ccc; padding: 10px;'>";
    echo $map_embed;
    echo "</div>";
} else {
    echo "<p style='color: red;'>Mapa není uložená!</p>";
}

echo "<h3>Všechny kontaktní konfigurace:</h3>";
echo "<ul>";
echo "<li>CONTACT_COMPANY_NAME: " . Configuration::get('CONTACT_COMPANY_NAME') . "</li>";
echo "<li>CONTACT_ADDRESS: " . Configuration::get('CONTACT_ADDRESS') . "</li>";
echo "<li>CONTACT_PHONE: " . Configuration::get('CONTACT_PHONE') . "</li>";
echo "<li>CONTACT_EMAIL: " . Configuration::get('CONTACT_EMAIL') . "</li>";
echo "<li>CONTACT_MAP_EMBED: " . (empty(Configuration::get('CONTACT_MAP_EMBED')) ? 'PRÁZDNÉ' : 'ULOŽENO') . "</li>";
echo "<li>CONTACT_BANK_INFO: " . Configuration::get('CONTACT_BANK_INFO') . "</li>";
echo "</ul>";
?>
