<?php
/**
 * Search Suggestions AJAX Endpoint
 * CIG Realizace - Phase 09
 * Provides auto-complete suggestions for search functionality
 */

header('Content-Type: application/json');

// Include required files
require_once '../includes/auth_check.php';
require_once '../classes/SearchEngine.php';

// Check if user is authenticated
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    // Get parameters
    $query = trim($_GET['q'] ?? '');
    $type = $_GET['type'] ?? 'all';
    $limit = min(15, max(5, intval($_GET['limit'] ?? 10)));
    
    // Validate query length
    if (strlen($query) < 2) {
        echo json_encode([]);
        exit;
    }
    
    // Initialize search engine
    $searchEngine = new SearchEngine();
    
    // Get suggestions based on type
    $suggestions = $searchEngine->getSearchSuggestions($query, $type, $limit);
    
    // Format response based on type
    if ($type === 'all') {
        $response = [
            'orders' => array_map(function($item) {
                return [
                    'id' => $item['id'],
                    'text' => $item['order_code'],
                    'subtitle' => $item['sales_rep_name'],
                    'type' => 'order'
                ];
            }, $suggestions['orders']),
            
            'catalog' => array_map(function($item) {
                return [
                    'text' => $item['catalog_code'],
                    'subtitle' => 'Použito ' . $item['count'] . 'x',
                    'type' => 'catalog'
                ];
            }, $suggestions['catalog']),
            
            'technology' => array_map(function($item) {
                return [
                    'text' => $item['technology'],
                    'subtitle' => 'Použito ' . $item['count'] . 'x',
                    'type' => 'technology'
                ];
            }, $suggestions['technology'])
        ];
    } else {
        // Single type response
        switch ($type) {
            case 'orders':
                $response = array_map(function($item) {
                    return [
                        'id' => $item['id'],
                        'text' => $item['order_code'],
                        'subtitle' => $item['sales_rep_name'],
                        'type' => 'order'
                    ];
                }, $suggestions);
                break;
                
            case 'catalog':
                $response = array_map(function($item) {
                    return [
                        'text' => $item['catalog_code'],
                        'subtitle' => 'Použito ' . $item['count'] . 'x',
                        'type' => 'catalog'
                    ];
                }, $suggestions);
                break;
                
            case 'technology':
                $response = array_map(function($item) {
                    return [
                        'text' => $item['technology'],
                        'subtitle' => 'Použito ' . $item['count'] . 'x',
                        'type' => 'technology'
                    ];
                }, $suggestions);
                break;
                
            default:
                $response = [];
        }
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Search suggestions error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
