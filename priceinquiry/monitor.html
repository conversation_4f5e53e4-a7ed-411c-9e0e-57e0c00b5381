<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PriceInquiry Monitor</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .monitor { border: 1px solid #ddd; padding: 15px; margin: 10px 0; background: #f9f9f9; }
        .log { background: #000; color: #0f0; padding: 10px; height: 300px; overflow-y: scroll; font-family: monospace; }
        button { padding: 10px 20px; margin: 5px; }
        .status { padding: 5px; margin: 5px 0; }
        .ok { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🔍 PriceInquiry Real-time Monitor</h1>

    <div class="monitor">
        <h2>Aktuální stav</h2>
        <div id="status"></div>
        <button onclick="checkStatus()">Aktualizovat stav</button>
        <button onclick="clearLog()">Vymazat log</button>
    </div>

    <div class="monitor">
        <h2>Real-time log</h2>
        <div id="log" class="log"></div>
    </div>

    <div class="monitor">
        <h2>Akce</h2>
        <button onclick="testPriceDetection()">Test detekce ceny</button>
        <button onclick="testVariantChange()">Simulovat zmenu varianty</button>
        <button onclick="testHookCall()">Test hook volani</button>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logLine = `[${timestamp}] ${message}\n`;
            logElement.textContent += logLine;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`PriceInquiry Monitor: ${message}`);
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        function checkStatus() {
            log('=== Kontrola stavu ===');
            
            // Kontrola JavaScript objektů
            if (typeof PriceInquiryJS !== 'undefined') {
                log('✓ PriceInquiryJS objekt existuje', 'ok');
            } else {
                log('✗ PriceInquiryJS objekt neexistuje', 'error');
            }
            
            if (typeof prestashop !== 'undefined') {
                log('✓ PrestaShop objekt existuje', 'ok');
            } else {
                log('✗ PrestaShop objekt neexistuje', 'error');
            }
            
            // Kontrola našich bloků
            const piBlocks = document.querySelectorAll('.price-inquiry-block-detail');
            log(`Nalezeno ${piBlocks.length} .price-inquiry-block-detail bloků`);
            
            piBlocks.forEach((block, index) => {
                const isVisible = window.getComputedStyle(block).display !== 'none';
                const initialPrice = block.getAttribute('data-initial-price');
                log(`  Blok ${index}: viditelný=${isVisible}, cena=${initialPrice}`);
            });
            
            // Kontrola cenových elementů
            const priceSelectors = ['.product-prices .price', '.current-price .price'];
            priceSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    elements.forEach((el, i) => {
                        log(`  ${selector}[${i}]: "${el.textContent.trim()}"`);
                    });
                }
            });
            
            updateStatusDisplay();
        }
        
        function updateStatusDisplay() {
            const piBlocks = document.querySelectorAll('.price-inquiry-block-detail');
            const visibleBlocks = Array.from(piBlocks).filter(block => 
                window.getComputedStyle(block).display !== 'none'
            );
            
            let statusHTML = '';
            statusHTML += `<div class="status ${piBlocks.length > 0 ? 'ok' : 'error'}">`;
            statusHTML += `Celkem bloků: ${piBlocks.length}, Viditelných: ${visibleBlocks.length}`;
            statusHTML += `</div>`;
            
            if (visibleBlocks.length > 1) {
                statusHTML += `<div class="status error">⚠ PROBLÉM: Více než 1 blok je viditelný!</div>`;
            }
            
            statusElement.innerHTML = statusHTML;
        }
        
        function testPriceDetection() {
            log('=== Test detekce ceny ===');
            
            if (typeof PriceInquiryJS !== 'undefined' && PriceInquiryJS.checkAndUpdatePriceInquiry) {
                log('Spouštím PriceInquiryJS.checkAndUpdatePriceInquiry()');
                PriceInquiryJS.checkAndUpdatePriceInquiry();
            } else {
                log('✗ PriceInquiryJS.checkAndUpdatePriceInquiry není dostupná', 'error');
            }
        }
        
        function testVariantChange() {
            log('=== Simulace změny varianty ===');
            
            if (typeof prestashop !== 'undefined' && prestashop.emit) {
                log('Spouštím prestashop.emit("updatedProduct")');
                prestashop.emit('updatedProduct', {
                    reason: 'test',
                    product_id: 9292
                });
            } else {
                log('✗ PrestaShop emit není dostupný', 'error');
            }
        }
        
        function testHookCall() {
            log('=== Test hook volání ===');
            
            // Simulujeme AJAX volání na náš diagnostický endpoint
            fetch('/modules/priceinquiry/diagnostics.php')
                .then(response => response.text())
                .then(data => {
                    log('✓ Diagnostický soubor je dostupný');
                })
                .catch(error => {
                    log('✗ Chyba při volání diagnostického souboru: ' + error, 'error');
                });
        }
        
        // Automatická kontrola každých 5 sekund
        setInterval(() => {
            updateStatusDisplay();
        }, 5000);
        
        // Posloucháme PrestaShop eventy
        if (typeof prestashop !== 'undefined') {
            prestashop.on('updateProduct', (event) => {
                log('🔄 PrestaShop event: updateProduct');
            });
            
            prestashop.on('updatedProduct', (event) => {
                log('✅ PrestaShop event: updatedProduct');
                setTimeout(() => {
                    checkStatus();
                }, 100);
            });
        }
        
        // Posloucháme naše vlastní logy
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('PriceInquiry:')) {
                log(message);
            }
            originalConsoleLog.apply(console, args);
        };
        
        // Počáteční kontrola
        setTimeout(checkStatus, 1000);
        
        log('Monitor spuštěn');
    </script>
</body>
</html>
