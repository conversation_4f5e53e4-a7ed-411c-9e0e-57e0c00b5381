<?php
/**
 * Save Filter AJAX Endpoint
 * CIG Realizace - Phase 09
 * Saves user filter configurations
 */

header('Content-Type: application/json');

// Include required files
require_once '../includes/auth_check.php';
require_once '../classes/FilterManager.php';

// Check if user is authenticated
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get current user
    $current_user = getCurrentUser();
    $user_id = $current_user['id'];
    
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON data']);
        exit;
    }
    
    // Validate required fields
    $name = trim($input['name'] ?? '');
    $filters = $input['filters'] ?? [];
    $is_public = $input['is_public'] ?? false;
    
    if (empty($name)) {
        http_response_code(400);
        echo json_encode(['error' => 'Filter name is required']);
        exit;
    }
    
    if (strlen($name) > 100) {
        http_response_code(400);
        echo json_encode(['error' => 'Filter name is too long (max 100 characters)']);
        exit;
    }
    
    // Only admin can create public filters
    if ($is_public && $current_user['role'] !== 'admin') {
        $is_public = false;
    }
    
    // Initialize filter manager
    $filterManager = new FilterManager();
    
    // Save the filter
    $result = $filterManager->saveFilter($user_id, $name, $filters, $is_public);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Filter byl úspěšně uložen'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to save filter']);
    }
    
} catch (Exception $e) {
    error_log("Save filter error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
