# Vlastn<PERSON> sekce "VLASTNÍ MODULY" v administraci PrestaShop

## Přehled
Tato funkcionalita automaticky vytváří vlastní sekci "VLASTNÍ MODULY" v administraci PrestaShop, kam se řadí všechny vaše custom moduly. Sekce se vytvoří pod "ROZŠÍŘENÍ" a má stejnou strukturu jako ostatn<PERSON> sek<PERSON> (OBCHOD, KONFIGURACE, atd.).

## Struktura menu po implementaci
```
ROZŠÍŘENÍ
├── Moduly
├── Vzhled
├── ...
└── Vlastní moduly          ← NOVÁ SEKCE
    ├── Katalogy           ← Váš modul
    ├── Další custom modul
    └── ...
```

## Jak to funguje

### 1. Automatické vytvoření sekce
- Při instalaci prvního custom modulu se automaticky vytvoří sekce "Vlastní moduly"
- Sekce má ikonu `extension` a umístí se pod "Rozšíření"
- Pokud sekce už existuje, použije se stávající

### 2. <PERSON><PERSON><PERSON> čištění
- Při odinstalaci posledního modulu ze sekce se sekce automaticky smaže
- Prázdné sekce se neponechávají v menu

## Implementace v novém modulu

### Základní použití
```php
// V metodě install() vašeho modulu
public function install()
{
    return parent::install() &&
        $this->createTab() &&
        // další instalační kroky...
}

// V metodě uninstall() vašeho modulu  
public function uninstall()
{
    return parent::uninstall() &&
        $this->removeTab() &&
        // další odinstalační kroky...
}

private function createTab()
{
    // Načti univerzální třídu
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    
    // Vytvoř tab v sekci "Vlastní moduly"
    return CustomModulesTabManager::createModuleTab(
        'AdminMujModul',    // Název controller třídy
        'Můj modul',        // Zobrazovaný název
        $this->name         // Název modulu
    );
}

private function removeTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::removeModuleTab('AdminMujModul');
}
```

### Pokročilé použití
```php
// Zkontrolovat, zda sekce existuje
if (CustomModulesTabManager::customModulesSectionExists()) {
    // Sekce existuje
}

// Získat seznam všech modulů v sekci
$custom_modules = CustomModulesTabManager::getCustomModulesTabs();

// Přesunout existující tab do vlastní sekce
CustomModulesTabManager::moveTabToCustomModules('AdminStaryModul');

// Získat informace o tabu
$tab_info = CustomModulesTabManager::getTabInfo('AdminMujModul');
```

## Soubory k zkopírování do nového modulu

### 1. CustomModulesTabManager.php
Zkopírujte soubor `classes/CustomModulesTabManager.php` do vašeho nového modulu:
```
vas_modul/
├── classes/
│   └── CustomModulesTabManager.php  ← Zkopírovat tento soubor
├── vas_modul.php
└── ...
```

### 2. Upravte hlavní soubor modulu
V hlavním PHP souboru vašeho modulu upravte metody `createTab()` a `removeTab()` podle příkladu výše.

## Správa a testování

### Test script
Pro testování a správu tabů použijte: `modules/katalogy/manage-custom-tabs.php`

Funkce:
- 🔧 Vytvořit sekci 'Vlastní moduly'
- 🗑️ Odstranit sekci 'Vlastní moduly'  
- 🔄 Přeinstalovat tab Katalogy
- 📋 Zobrazit aktuální strukturu tabů

### Ruční správa
```php
// Vytvořit sekci ručně
CustomModulesTabManager::createOrGetCustomModulesTab();

// Vyčistit prázdnou sekci
CustomModulesTabManager::cleanupCustomModulesTabIfEmpty();
```

## Výhody

### 1. Organizace
- Všechny custom moduly na jednom místě
- Čistší struktura administrace
- Oddělení od standardních PrestaShop modulů

### 2. Automatizace
- Automatické vytváření a mazání sekce
- Žádná ruční správa
- Konzistentní chování napříč moduly

### 3. Univerzálnost
- Jedna třída pro všechny custom moduly
- Snadná implementace
- Kompatibilita s různými verzemi PrestaShop

## Kompatibilita
- PrestaShop 1.7+
- PrestaShop 8.x
- Automatická detekce správného parent tabu

## Příklad implementace
Modul Katalogy už tuto funkcionalitu používá - můžete se podívat na implementaci v `katalogy.php` (metody `createTab()` a `removeTab()`).

## Troubleshooting

### Sekce se nevytváří
1. Zkontrolujte oprávnění k databázi
2. Ověřte, že třída `CustomModulesTabManager` je správně načtena
3. Zkontrolujte logy PrestaShop

### Tab se nezobrazuje
1. Vyčistěte cache administrace
2. Zkontrolujte, že controller třída existuje
3. Ověřte oprávnění uživatele

### Sekce se nesmaže
1. Zkontrolujte, že všechny child taby jsou skutečně smazané
2. Ověřte v databázi tabulku `ps_tab`
