/* views/css/contactmodule.css */

.contact-module {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: inherit;
}

/* <PERSON><PERSON><PERSON><PERSON>, <PERSON>e styly fungují i v CMS obsahu */
.cms .contact-module,
#content .contact-module,
.page-content .contact-module {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* <PERSON><PERSON> sekce - 2 sloupce */
.contact-top-section {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.contact-info {
    flex: 1;
    min-width: 300px;
}

.contact-map {
    flex: 1;
    min-width: 300px;
}

.contact-info h3,
.contact-map h3,
.representatives-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 24px;
    border-bottom: 2px solid #007acc;
    padding-bottom: 10px;
}

.company-info h4 {
    color: #007acc;
    font-size: 20px;
    margin-bottom: 15px;
}

.address {
    margin-bottom: 20px;
    line-height: 1.6;
    color: #666;
}

.contact-details {
    margin-bottom: 25px;
}

.contact-details > div {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-details i {
    color: #007acc;
    width: 16px;
}

.contact-details a {
    color: #007acc;
    text-decoration: none;
}

.contact-details a:hover {
    text-decoration: underline;
}

.bank-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007acc;
}

.bank-info h5 {
    margin-bottom: 10px;
    color: #333;
}

.bank-details {
    line-height: 1.6;
    color: #666;
}

/* Mapa */
.map-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.map-container {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.map-container iframe {
    width: 100% !important;
    height: 100% !important;
    border: 0 !important;
    display: block !important;
}

/* Obchodní zástupci */
.representatives-section {
    margin-top: 40px;
}

.representatives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.representative-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.representative-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.rep-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.rep-name {
    color: #007acc;
    font-size: 18px;
    margin-bottom: 5px;
}

.rep-position {
    color: #666;
    font-style: italic;
    margin: 0;
}

.rep-contact > div {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.rep-contact i {
    color: #007acc;
    width: 16px;
}

.rep-contact a {
    color: #007acc;
    text-decoration: none;
}

.rep-contact a:hover {
    text-decoration: underline;
}

.rep-region {
    color: #666;
    font-size: 14px;
}

/* Kanceláře */
.offices-section {
    margin-top: 40px;
}

.offices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.office-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.office-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.office-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.office-name {
    color: #007acc;
    font-size: 18px;
    margin-bottom: 10px;
}

.office-address {
    color: #666;
    line-height: 1.6;
    margin-bottom: 10px;
}

.office-contact > div {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.office-contact i {
    color: #007acc;
    width: 16px;
}

.office-contact a {
    color: #007acc;
    text-decoration: none;
}

.office-contact a:hover {
    text-decoration: underline;
}

.office-bank-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-top: 15px;
    border-left: 3px solid #007acc;
}

.office-bank-info h5 {
    margin-bottom: 8px;
    color: #333;
    font-size: 14px;
}

.office-bank-info .bank-details {
    line-height: 1.6;
    color: #666;
    font-size: 14px;
}

/* Responzivní design */
@media (max-width: 768px) {
    .contact-top-section {
        flex-direction: column;
        gap: 20px;
    }
    
    .contact-info,
    .contact-map {
        min-width: auto;
    }
    
    .representatives-grid,
    .offices-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .representative-card,
    .office-card {
        padding: 20px;
    }
    
    .contact-module {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .contact-module {
        padding: 10px;
    }
    
    .representative-card,
    .office-card {
        padding: 15px;
    }

    .contact-info h3,
    .contact-map h3,
    .representatives-section h3,
    .offices-section h3 {
        font-size: 20px;
    }
    
    .company-info h4 {
        font-size: 18px;
    }
}

/* Drag and Drop styly pro administraci */
.sortable-container {
    min-height: 50px;
}

.sortable-item {
    transition: transform 0.2s ease;
}

.sortable-ghost {
    opacity: 0.5;
    background: #f0f0f0;
}

.drag-handle {
    color: #999;
    cursor: move !important;
}

.drag-handle:hover {
    color: #333;
}

.sortable-item:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}
