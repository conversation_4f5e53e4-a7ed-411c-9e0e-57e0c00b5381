<?php
/**
 * Test soubor pro kontrolu modulu PriceInquiry
 */

// Načteme PrestaShop
require_once dirname(__FILE__) . '/../../config/config.inc.php';

echo "<h1>Test modulu PriceInquiry</h1>";

// Zkontrolujeme, jestli modul existuje
if (Module::isInstalled('priceinquiry')) {
    echo "<p style='color: green;'>✓ Modul je nainstalován</p>";
    
    if (Module::isEnabled('priceinquiry')) {
        echo "<p style='color: green;'>✓ Modul je aktivní</p>";
    } else {
        echo "<p style='color: red;'>✗ Modul není aktivní</p>";
    }
    
    // Zkusíme načíst instanci modulu
    $module = Module::getInstanceByName('priceinquiry');
    if ($module) {
        echo "<p style='color: green;'>✓ Instance modulu načtena</p>";
        echo "<p>Verze: " . $module->version . "</p>";
        echo "<p>Název: " . $module->displayName . "</p>";
        
        // Zkontrolujeme hooky
        $hooks = Hook::getHooks();
        $moduleHooks = [];
        foreach ($hooks as $hook) {
            if (Hook::isModuleRegisteredOnHook($module, $hook['name'], Shop::getContextShopID())) {
                $moduleHooks[] = $hook['name'];
            }
        }

        echo "<p>Registrované hooky: " . implode(', ', $moduleHooks) . "</p>";
        
    } else {
        echo "<p style='color: red;'>✗ Nepodařilo se načíst instanci modulu</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ Modul není nainstalován</p>";
}

// Zkontrolujeme konfiguraci
$enabled = Configuration::get('PRICE_INQUIRY_ENABLED');
echo "<p>PRICE_INQUIRY_ENABLED: " . ($enabled ? 'true' : 'false') . "</p>";

echo "<h2>Test na konkrétním produktu</h2>";

// Zkusíme načíst produkt s ID z URL
$productId = 9292; // ID z URL
$product = new Product($productId);

if (Validate::isLoadedObject($product)) {
    echo "<p style='color: green;'>✓ Produkt načten: " . $product->name . "</p>";
    
    $defaultAttribute = Product::getDefaultAttribute($productId);
    echo "<p>Default attribute: " . $defaultAttribute . "</p>";
    
    $price = Product::getPriceStatic($productId, true, $defaultAttribute);
    echo "<p>Cena: " . $price . " Kč</p>";
    
    if ($price <= 0) {
        echo "<p style='color: orange;'>→ Tento produkt by měl zobrazovat 'Cena na dotaz'</p>";
    } else {
        echo "<p style='color: blue;'>→ Tento produkt má normální cenu</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ Nepodařilo se načíst produkt s ID " . $productId . "</p>";
}
