<?php
/**
 * Debug skript pro ověření generování URL
 */

// Ko<PERSON><PERSON>a, že je skript spuštěn z PrestaShop prostředí
if (!defined('_PS_VERSION_')) {
    require_once dirname(__FILE__) . '/../../config/config.inc.php';
}

echo "<h2>Debug generování URL pro technologie</h2>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .tech-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .url { font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; }
    .debug-info { background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 3px; }
</style>\n";

// Načtení technologií z databáze
$sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'technologie` WHERE active = 1 ORDER BY position ASC, name ASC';
$results = Db::getInstance()->executeS($sql);

echo "<h3>Technologie v databázi:</h3>\n";
if (!$results) {
    echo "<div class='error'>❌ Žádné technologie nenalezeny v databázi!</div>\n";
    exit;
}

echo "<p>Nalezeno " . count($results) . " technologií:</p>\n";

foreach ($results as $row) {
    $hasSlug = !empty($row['slug']);
    $class = $hasSlug ? 'success' : 'error';
    
    echo "<div class='tech-item $class'>\n";
    echo "<h4>" . htmlspecialchars($row['name']) . "</h4>\n";
    echo "<div class='debug-info'>\n";
    echo "<strong>ID:</strong> " . $row['id_technologie'] . "<br>\n";
    echo "<strong>Slug:</strong> " . ($row['slug'] ? '<code>' . htmlspecialchars($row['slug']) . '</code>' : '❌ CHYBÍ') . "<br>\n";
    echo "<strong>Active:</strong> " . ($row['active'] ? '✅ Ano' : '❌ Ne') . "<br>\n";
    echo "<strong>Position:</strong> " . $row['position'] . "<br>\n";
    echo "</div>\n";
    
    if ($hasSlug) {
        // Simulace generování URL stejně jako v controlleru
        $baseUrl = Tools::getHttpHost(true);
        $customUrl = $baseUrl . '/reklamni-potisk/' . $row['slug'];
        $fallbackUrl = Context::getContext()->link->getModuleLink('technologie', 'technologie', ['slug' => $row['slug']]);
        
        echo "<div class='debug-info'>\n";
        echo "<strong>Generované URL:</strong><br>\n";
        echo "Custom URL: <span class='url'>" . htmlspecialchars($customUrl) . "</span><br>\n";
        echo "Fallback URL: <span class='url'>" . htmlspecialchars($fallbackUrl) . "</span><br>\n";
        echo "</div>\n";
        
        echo "<p><a href='" . htmlspecialchars($customUrl) . "' target='_blank'>🔗 Test custom URL</a></p>\n";
        echo "<p><a href='" . htmlspecialchars($fallbackUrl) . "' target='_blank'>🔗 Test fallback URL</a></p>\n";
    } else {
        echo "<div class='error'>⚠️ Technologie nemá slug - tlačítko bude disabled</div>\n";
    }
    
    echo "</div>\n";
}

echo "<h3>Simulace controlleru:</h3>\n";
echo "<div class='debug-info'>\n";
echo "<strong>Metoda getTechnologieFromDatabase():</strong><br>\n";

// Simulace stejné logiky jako v controlleru
$technologie = [];
foreach ($results as $row) {
    $tech = new stdClass();
    $tech->id = (int)$row['id_technologie'];
    $tech->name = $row['name'];
    $tech->slug = $row['slug'] ?? null;
    
    // Simulace nastavení detail_url
    if ($tech->slug) {
        $baseUrl = Tools::getHttpHost(true);
        $tech->detail_url = $baseUrl . '/reklamni-potisk/' . $tech->slug;
        $status = '✅ URL nastavena';
    } else {
        $tech->detail_url = null;
        $status = '❌ Žádná URL (chybí slug)';
    }
    
    echo "- " . htmlspecialchars($tech->name) . ": " . $status;
    if ($tech->detail_url) {
        echo " → <code>" . htmlspecialchars($tech->detail_url) . "</code>";
    }
    echo "<br>\n";
    
    $technologie[] = $tech;
}

echo "</div>\n";

echo "<h3>Template logika:</h3>\n";
echo "<div class='debug-info'>\n";
echo "<strong>V template technologie.tpl:</strong><br>\n";
echo "<code>\n";
foreach ($technologie as $tech) {
    echo "Tech: " . htmlspecialchars($tech->name) . "<br>\n";
    echo "&nbsp;&nbsp;isset(\$tech->detail_url): " . (isset($tech->detail_url) ? 'TRUE' : 'FALSE') . "<br>\n";
    echo "&nbsp;&nbsp;\$tech->detail_url: " . ($tech->detail_url ? htmlspecialchars($tech->detail_url) : 'NULL/EMPTY') . "<br>\n";
    echo "&nbsp;&nbsp;isset(\$tech->slug): " . (isset($tech->slug) ? 'TRUE' : 'FALSE') . "<br>\n";
    echo "&nbsp;&nbsp;\$tech->slug: " . ($tech->slug ? htmlspecialchars($tech->slug) : 'NULL/EMPTY') . "<br>\n";

    // Simulace template logiky
    $templateUrl = '';
    if ($tech->detail_url) {
        $templateUrl = $tech->detail_url;
        $method = 'detail_url';
    } elseif ($tech->slug) {
        $templateUrl = '/reklamni-potisk/' . $tech->slug;
        $method = 'fallback';
    }

    echo "&nbsp;&nbsp;Template URL: " . ($templateUrl ? htmlspecialchars($templateUrl) : 'ŽÁDNÁ') . " (metoda: $method)<br>\n";
    echo "&nbsp;&nbsp;Výsledek: " . ($templateUrl ? 'Zobrazí se odkaz' : 'Zobrazí se disabled tlačítko') . "<br><br>\n";
}
echo "</code>\n";
echo "</div>\n";

echo "<h3>Doporučení:</h3>\n";
echo "<div class='debug-info'>\n";
if (count(array_filter($results, function($r) { return empty($r['slug']); })) > 0) {
    echo "❌ <strong>Problém:</strong> Některé technologie nemají nastavený slug!<br>\n";
    echo "🔧 <strong>Řešení:</strong> Spusťte update_database.php pro nastavení slug hodnot.<br>\n";
} else {
    echo "✅ <strong>Všechny technologie mají slug</strong> - URL by se měly generovat správně.<br>\n";
    echo "🔍 Pokud stále vidíte špatné URL, problém může být v cache nebo v template logice.<br>\n";
}
echo "</div>\n";
