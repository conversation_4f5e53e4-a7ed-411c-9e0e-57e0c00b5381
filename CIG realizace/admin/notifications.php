<?php
/**
 * Admin Notifications Management
 * CIG Realizace - Phase 08
 */

require_once '../includes/auth_check.php';
require_once '../includes/email_functions.php';

// Check admin access
if (!hasRole('admin')) {
    header('Location: ../includes/access_denied.php');
    exit;
}

$current_user = getCurrentUser();
$page_title = 'Správa notifikací';

// Handle actions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_email_config':
            $tests = testEmailConfiguration();
            $allPassed = array_reduce($tests, function($carry, $test) {
                return $carry && $test;
            }, true);
            
            if ($allPassed) {
                $message = 'Konfigurace emailů je v pořádku.';
            } else {
                $error = 'Některé testy konfigurace selhaly. Zkontrolujte nastavení.';
            }
            break;
            
        case 'send_test_email':
            $testEmail = $_POST['test_email'] ?? '';
            if (!empty($testEmail) && filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
                $emailManager = new EmailManager();
                $success = $emailManager->sendEmailNow(
                    $testEmail,
                    'Test User',
                    'Test email z CIG Realizace',
                    'order_completed',
                    [
                        'order_code' => 'TEST-001',
                        'sales_rep_name' => 'Test User',
                        'order_date' => date('d.m.Y'),
                        'completion_date' => date('d.m.Y H:i'),
                        'items_count' => 5,
                        'technologies' => 'Test technologie',
                        'order_url' => '#'
                    ]
                );
                
                if ($success) {
                    $message = "Test email byl odeslán na adresu: $testEmail";
                } else {
                    $error = 'Nepodařilo se odeslat test email.';
                }
            } else {
                $error = 'Zadejte platnou emailovou adresu.';
            }
            break;
            
        case 'process_queue':
            $processed = processEmailQueue();
            $message = "Zpracováno $processed emailů z fronty.";
            break;
            
        case 'clean_queue':
            $cleaned = cleanOldEmailQueue(30);
            $message = "Vyčištěno $cleaned starých emailů z fronty.";
            break;
            
        case 'send_overdue_alert':
            $sent = sendOverdueOrdersAlert();
            if ($sent) {
                $message = 'Upozornění na zpožděné objednávky bylo odesláno.';
            } else {
                $error = 'Nepodařilo se odeslat upozornění nebo nejsou žádné zpožděné objednávky.';
            }
            break;
            
        case 'send_daily_summaries':
            $sent = sendDailySummaryEmails();
            $message = "Odesláno $sent denních souhrnů.";
            break;
    }
}

// Get statistics
$queueStats = getEmailQueueStats();
$deliveryStats = getEmailDeliveryStats(7); // Last 7 days
$configTests = testEmailConfiguration();
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
    <style>
        .config-test {
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .config-test.passed {
            background: #d1edff;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .config-test.failed {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .queue-actions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../dashboard.php">
                                <i class="fas fa-home"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i> Uživatelé
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="notifications.php">
                                <i class="fas fa-bell"></i> Notifikace
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="email_queue.php">
                                <i class="fas fa-envelope"></i> Email fronta
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="system_activity.php">
                                <i class="fas fa-chart-line"></i> Systémová aktivita
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-bell text-primary"></i>
                        <?= htmlspecialchars($page_title) ?>
                    </h1>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Email Queue Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number"><?= $queueStats['pending'] ?? 0 ?></div>
                            <div>Čekající emaily</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number"><?= $queueStats['sent'] ?? 0 ?></div>
                            <div>Odeslané emaily</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number"><?= $queueStats['failed'] ?? 0 ?></div>
                            <div>Neúspěšné</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-number"><?= $queueStats['total'] ?? 0 ?></div>
                            <div>Celkem</div>
                        </div>
                    </div>
                </div>

                <!-- Configuration Tests -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Konfigurace emailů</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="config-test <?= $configTests['smtp_configured'] ? 'passed' : 'failed' ?>">
                                    <i class="fas <?= $configTests['smtp_configured'] ? 'fa-check' : 'fa-times' ?>"></i>
                                    SMTP konfigurace
                                </div>
                                <div class="config-test <?= $configTests['templates_exist'] ? 'passed' : 'failed' ?>">
                                    <i class="fas <?= $configTests['templates_exist'] ? 'fa-check' : 'fa-times' ?>"></i>
                                    Email templates
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="config-test <?= $configTests['database_tables'] ? 'passed' : 'failed' ?>">
                                    <i class="fas <?= $configTests['database_tables'] ? 'fa-check' : 'fa-times' ?>"></i>
                                    Databázové tabulky
                                </div>
                                <div class="config-test <?= $configTests['queue_writable'] ? 'passed' : 'failed' ?>">
                                    <i class="fas <?= $configTests['queue_writable'] ? 'fa-check' : 'fa-times' ?>"></i>
                                    Email fronta
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <form method="post" class="d-inline">
                                <input type="hidden" name="action" value="test_email_config">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-sync"></i> Otestovat konfiguraci
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Queue Actions -->
                <div class="queue-actions">
                    <h5><i class="fas fa-tasks"></i> Akce s frontou</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <form method="post" class="mb-2">
                                <input type="hidden" name="action" value="process_queue">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-play"></i> Zpracovat frontu
                                </button>
                            </form>
                            
                            <form method="post" class="mb-2">
                                <input type="hidden" name="action" value="clean_queue">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-broom"></i> Vyčistit staré emaily
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form method="post" class="mb-2">
                                <input type="hidden" name="action" value="send_overdue_alert">
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-exclamation-triangle"></i> Odeslat upozornění na zpoždění
                                </button>
                            </form>
                            
                            <form method="post" class="mb-2">
                                <input type="hidden" name="action" value="send_daily_summaries">
                                <button type="submit" class="btn btn-info w-100">
                                    <i class="fas fa-chart-bar"></i> Odeslat denní souhrny
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Test Email -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-envelope-open"></i> Test email</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <input type="hidden" name="action" value="send_test_email">
                            <div class="row">
                                <div class="col-md-8">
                                    <input type="email" name="test_email" class="form-control" 
                                           placeholder="Zadejte emailovou adresu pro test" required>
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-paper-plane"></i> Odeslat test
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Recent Delivery Stats -->
                <?php if (!empty($deliveryStats)): ?>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Statistiky doručení (posledních 7 dní)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Datum</th>
                                        <th>Odesláno</th>
                                        <th>Doručeno</th>
                                        <th>Otevřeno</th>
                                        <th>Kliknuto</th>
                                        <th>Bounced</th>
                                        <th>Úspěšnost</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($deliveryStats as $stat): ?>
                                    <tr>
                                        <td><?= date('d.m.Y', strtotime($stat['date'])) ?></td>
                                        <td><?= $stat['total_sent'] ?></td>
                                        <td><?= $stat['delivered'] ?></td>
                                        <td><?= $stat['opened'] ?></td>
                                        <td><?= $stat['clicked'] ?></td>
                                        <td><?= $stat['bounced'] ?></td>
                                        <td>
                                            <?php 
                                            $successRate = $stat['total_sent'] > 0 ? 
                                                round(($stat['delivered'] / $stat['total_sent']) * 100, 1) : 0;
                                            ?>
                                            <span class="badge bg-<?= $successRate >= 90 ? 'success' : ($successRate >= 70 ? 'warning' : 'danger') ?>">
                                                <?= $successRate ?>%
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
