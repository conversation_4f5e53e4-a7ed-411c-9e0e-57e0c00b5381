{*
* Blok pro detail produktu, v<PERSON><PERSON><PERSON><PERSON><PERSON> do `displayProductActions`.
*}

{* Debug informace *}
<!-- PriceInquiry Debug: show_block={$show_block|var_dump}, current_price={$current_price} -->

{* Zobrazujeme blok pouze když má být zobrazen - kontrolujeme přesně *}
{if $show_block && $current_price <= 0}
<style>
    /* Skryjeme cenu - použijeme správné selektory */
    .product-price,
    .product-prices {
        display: none !important;
    }
    /* Najdeme rodiče a v něm skryjeme jen to, co je potřeba */
    .product-add-to-cart .product-quantity,
    .product-add-to-cart .add {
        display: none !important;
    }
    /* Náš blok se zobrazí jako první */
    .price-inquiry-block-detail {
        order: -1;
        width: 100%;
        margin-bottom: 15px;
    }
</style>

<div class="price-inquiry-block-detail" style="display: block;" data-initial-price="{$current_price}" data-show-block="true">
    <div class="pi-price-text-wrapper">
        <span class="pi-price-text">
            {l s='Cena na dotaz' mod='priceinquiry'}
        </span>
    </div>

    <div class="pi-container-simple">
        <button type="button"
                class="btn btn-primary pi-btn"
                data-product-id="{$product_id}"
                data-product-name="{$product_name|escape:'html':'UTF-8'}"
                data-product-reference="{$product_reference|escape:'html':'UTF-8'}"
                data-product-image="{$product_image|escape:'html':'UTF-8'}"
                data-is-logged="{if $is_logged}1{else}0{/if}"
                {if $is_logged}
                data-customer-name="{$customer_name|escape:'html':'UTF-8'}"
                data-customer-email="{$customer_email|escape:'html':'UTF-8'}"
                {/if}>
            <i class="material-icons">contact_mail</i>
            {l s='Zjistit cenu' mod='priceinquiry'}
        </button>
    </div>
</div>
{else}
{* Skrytý blok pro JavaScript - zobrazí se při změně varianty na nulovou cenu *}
<div class="price-inquiry-block-detail" style="display: none;" data-initial-price="{$current_price}" data-show-block="false">
    <div class="pi-price-text-wrapper">
        <span class="pi-price-text">
            {l s='Cena na dotaz' mod='priceinquiry'}
        </span>
    </div>

    <div class="pi-container-simple">
        <button type="button"
                class="btn btn-primary pi-btn"
                data-product-id="{$product_id}"
                data-product-name="{$product_name|escape:'html':'UTF-8'}"
                data-product-reference="{$product_reference|escape:'html':'UTF-8'}"
                data-product-image="{$product_image|escape:'html':'UTF-8'}"
                data-is-logged="{if $is_logged}1{else}0{/if}"
                {if $is_logged}
                data-customer-name="{$customer_name|escape:'html':'UTF-8'}"
                data-customer-email="{$customer_email|escape:'html':'UTF-8'}"
                {/if}>
            <i class="material-icons">contact_mail</i>
            {l s='Zjistit cenu' mod='priceinquiry'}
        </button>
    </div>
</div>
{/if}