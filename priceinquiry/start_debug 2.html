<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Spuštěn<PERSON> diagnostiky</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .step { background: #f0f0f0; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
        .code { background: #000; color: #0f0; padding: 10px; font-family: monospace; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Diagnostika přepínání variant</h1>
    
    <div class="step">
        <h2>Krok 1: Příprava</h2>
        <p>1. Jdi na stránku produktu s nulovou cenou (černá varianta)</p>
        <p>2. <PERSON><PERSON><PERSON><PERSON><PERSON> konzo<PERSON> (F12)</p>
        <p>3. Klikni na tlačítko níže nebo zkopíruj kód do konzole</p>
        
        <button onclick="loadDebugScript()">Spustit diagnostiku</button>
        
        <div class="code">
fetch('/modules/priceinquiry/debug_variant_switch.js')
  .then(r => r.text())
  .then(code => eval(code));
        </div>
    </div>
    
    <div class="step">
        <h2>Krok 2: Test</h2>
        <p>1. Po spuštění diagnostiky přepni na variantu s cenou (bílá)</p>
        <p>2. Sleduj výstup v konzoli</p>
        <p>3. Počkej 3-5 sekund</p>
    </div>
    
    <div class="step">
        <h2>Krok 3: Report</h2>
        <p>Zavolej v konzoli:</p>
        <div class="code">variantDebug.getReport()</div>
        <p>A zkopíruj celý výstup konzole</p>
    </div>
    
    <script>
        function loadDebugScript() {
            fetch('/modules/priceinquiry/debug_variant_switch.js')
                .then(r => r.text())
                .then(code => {
                    eval(code);
                    alert('Diagnostika spuštěna! Přepni variantu a sleduj konzoli.');
                })
                .catch(err => {
                    alert('Chyba při načítání: ' + err);
                });
        }
    </script>
</body>
</html>
