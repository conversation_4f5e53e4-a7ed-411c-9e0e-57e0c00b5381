/**
 * History Page Styles
 * CIG Realizace - Phase 06
 */

/* History Timeline */
.history-timeline {
    position: relative;
    padding-left: 30px;
}

.history-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #6c757d);
}

.history-item {
    position: relative;
    margin-bottom: 25px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.history-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.history-item::before {
    content: '';
    position: absolute;
    left: -37px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #007bff;
}

.history-item.preview-status-change::before {
    background: #007bff;
    box-shadow: 0 0 0 2px #007bff;
}

.history-item.delivery-date-change::before {
    background: #ffc107;
    box-shadow: 0 0 0 2px #ffc107;
}

.history-item.technology-assignment::before {
    background: #17a2b8;
    box-shadow: 0 0 0 2px #17a2b8;
}

.history-item.item-relevance-change::before {
    background: #6c757d;
    box-shadow: 0 0 0 2px #6c757d;
}

.history-item.inventory-status-change::before {
    background: #28a745;
    box-shadow: 0 0 0 2px #28a745;
}

.history-item.order-completion::before {
    background: #28a745;
    box-shadow: 0 0 0 2px #28a745;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.history-action {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #495057;
}

.history-action i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.history-meta {
    font-size: 0.875rem;
    color: #6c757d;
    display: flex;
    align-items: center;
}

.history-meta i {
    margin-right: 5px;
}

.history-description {
    color: #495057;
    line-height: 1.5;
    margin-bottom: 10px;
}

.history-user {
    font-size: 0.875rem;
    color: #6c757d;
    display: flex;
    align-items: center;
}

.history-user i {
    margin-right: 5px;
}

.history-details {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
    font-size: 0.875rem;
}

.history-details .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.history-details .detail-row:last-child {
    margin-bottom: 0;
}

.history-details .detail-label {
    font-weight: 600;
    color: #495057;
}

.history-details .detail-value {
    color: #6c757d;
}

/* Value changes */
.value-change {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.old-value {
    background: #f8d7da;
    color: #721c24;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.875rem;
    text-decoration: line-through;
}

.new-value {
    background: #d4edda;
    color: #155724;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
}

.change-arrow {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Filters */
.history-filters {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

/* Loading states */
.history-loading {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.history-loading .spinner-border {
    margin-bottom: 15px;
}

/* Empty state */
.history-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.history-empty i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Responsive design */
@media (max-width: 768px) {
    .history-timeline {
        padding-left: 20px;
    }
    
    .history-timeline::before {
        left: 10px;
    }
    
    .history-item::before {
        left: -27px;
    }
    
    .history-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .history-meta {
        align-self: flex-end;
    }
    
    .history-details .detail-row {
        flex-direction: column;
        gap: 2px;
    }
}

/* Animation for new items */
.history-item.fade-in {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Export button */
.export-btn {
    position: relative;
    overflow: hidden;
}

.export-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.export-btn:hover::after {
    width: 300px;
    height: 300px;
}

/* Status badges in history */
.history-item .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Additional data display */
.additional-data {
    background: #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin-top: 8px;
    font-size: 0.8rem;
}

.additional-data .data-label {
    font-weight: 600;
    color: #495057;
}

.additional-data .data-value {
    color: #6c757d;
}

/* Action type specific styling */
.action-preview-status-change {
    border-left: 4px solid #007bff;
}

.action-delivery-date-change {
    border-left: 4px solid #ffc107;
}

.action-technology-assignment {
    border-left: 4px solid #17a2b8;
}

.action-item-relevance-change {
    border-left: 4px solid #6c757d;
}

.action-inventory-status-change {
    border-left: 4px solid #28a745;
}

.action-order-completion {
    border-left: 4px solid #28a745;
}
