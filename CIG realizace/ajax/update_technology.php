<?php
/**
 * AJAX endpoint for updating item technology assignment
 * CIG Realizace - Phase 05
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and history logger
require_once '../includes/auth_check.php';
require_once '../classes/HistoryLogger.php';

// Check if user is logged in and has permission
$current_user = getCurrentUser();
if (!$current_user || !in_array($current_user['role'], ['admin', 'obchodnik', 'grafik'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Nemáte oprávnění k této akci']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

// Get and validate input
$item_id = intval($_POST['item_id'] ?? 0);
$technology = trim($_POST['technology'] ?? '');

if (!$item_id) {
    echo json_encode(['success' => false, 'message' => 'Neplatné ID položky']);
    exit;
}

// Validate technology length
if (strlen($technology) > 100) {
    echo json_encode(['success' => false, 'message' => 'Technologie může mít maximálně 100 znaků']);
    exit;
}

try {
    $pdo = getDbConnection();
    $pdo->beginTransaction();
    
    // Get current item data
    $stmt = $pdo->prepare("
        SELECT oi.*, o.order_code, o.id as order_id
        FROM order_items oi
        LEFT JOIN orders o ON oi.order_id = o.id
        WHERE oi.id = ?
    ");
    $stmt->execute([$item_id]);
    $item = $stmt->fetch();
    
    if (!$item) {
        throw new Exception('Položka nebyla nalezena');
    }
    
    $old_technology = $item['technology_assignment'];
    
    // Update technology assignment
    $stmt = $pdo->prepare("
        UPDATE order_items 
        SET technology_assignment = ?, updated_at = ? 
        WHERE id = ?
    ");
    $stmt->execute([$technology, date('Y-m-d H:i:s'), $item_id]);
    
    // Log the change using HistoryLogger
    HistoryLogger::logTechnologyAssignment(
        $item['order_id'],
        $current_user['id'],
        $item_id,
        $item['catalog_code'],
        $old_technology,
        $technology
    );
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Technologie byla úspěšně přiřazena',
        'data' => [
            'item_id' => $item_id,
            'old_technology' => $old_technology,
            'new_technology' => $technology,
            'catalog_code' => $item['catalog_code']
        ]
    ]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error updating technology: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při aktualizaci technologie: ' . $e->getMessage()
    ]);
}
?>
