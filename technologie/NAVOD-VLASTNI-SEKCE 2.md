# Návod: Přidání vlastní sekce do modulu Technologie

## ✅ Co bylo opraveno

Problém byl v špatném umístění souborů. Soubory z balíčku `custom-modules-section` by<PERSON> z<PERSON> do `classes/custom-modules-section/`, ale měly být:

- **`CustomModulesTabManager.php`** → `classes/CustomModulesTabManager.php` ✅ OPRAVENO
- **Ostatní soubory** → přímo do kořenu modulu

## 🚀 Jak pokračovat

### 1. Spusťte script pro přidání vlastní sekce:
```
https://czimg-dev1.www2.peterman.cz/modules/technologie/add-custom-modules-section.php
```

### 2. Postupujte podle instrukcí na stránce:
1. **Vytvořte sekci** (pokud neexistuje)
2. **Přesuňte tab** do vlastní sekce
3. **Aktualizujte kód** modulu

## 📋 Správná struktura souborů

```
technologie/
├── classes/
│   ├── CustomModulesTabManager.php     ✅ SPRÁVNĚ
│   └── ...
├── technologie.php
├── add-custom-modules-section.php      ✅ NOVÝ SCRIPT
└── ...
```

## 🔧 Co script udělá

1. **Zkontroluje** existenci modulu a potřebných souborů
2. **Vytvoří** sekci "Vlastní moduly" (pokud neexistuje)
3. **Přesune** tab Technologie do vlastní sekce
4. **Aktualizuje** kód modulu automaticky

## 📝 Ruční úprava kódu (pokud automatická nefunguje)

Pokud automatická aktualizace kódu nefunguje, upravte ručně v `technologie.php`:

### Najděte metodu `createTab()` a nahraďte ji:
```php
private function createTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::createModuleTab('AdminTechnologie', 'Technologie', $this->name);
}
```

### Najděte metodu `removeTab()` a nahraďte ji:
```php
private function removeTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::removeModuleTab('AdminTechnologie');
}
```

## 🎯 Výsledek

Po dokončení bude modul Technologie umístěn v sekci "VLASTNÍ MODULY" hned po KONFIGURACE:

```
KONFIGURACE
├── Nastavení eshopu
└── Nástroje

VLASTNÍ MODULY              ← NOVÁ SEKCE
├── Katalogy               
├── Technologie            ← VÁŠ MODUL
└── ...

ADVANCE SEO
├── SEO Configuration
└── ...
```

## 🔍 Řešení problémů

### Script hlásí chyby:
1. Zkontrolujte, že `CustomModulesTabManager.php` je v `classes/`
2. Ověřte oprávnění k souborům
3. Zkontrolujte logy PrestaShop

### Tab se nezobrazuje:
1. Vyčistěte cache administrace
2. Obnovte stránku administrace
3. Zkontrolujte oprávnění uživatele

## 📞 Další pomoc

Pokud máte problémy, spusťte script a postupujte podle instrukcí. Script automaticky diagnostikuje a opraví většinu problémů.
