<?php
require_once '../includes/session.php';
require_once '../includes/auth_check.php';
require_once '../config/database.php';
require_once '../classes/HistoryLogger.php';

// Check admin access
if ($current_user['role'] !== 'admin') {
    header('Location: ../includes/access_denied.php');
    exit;
}

$page_title = 'Systémová aktivita';

// Get filters
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$action_type = $_GET['action_type'] ?? '';
$user_id = $_GET['user_id'] ?? '';
$limit = intval($_GET['limit'] ?? 50);

try {
    $pdo = getDbConnection();
    
    // Get all users for filter
    $stmt = $pdo->query("SELECT id, username, full_name FROM users WHERE is_active = 1 ORDER BY full_name");
    $users = $stmt->fetchAll();
    
    // Get activity data with filters
    $sql = "
        SELECT oh.*, u.username, u.full_name, u.role, o.order_code
        FROM order_history oh
        LEFT JOIN users u ON oh.user_id = u.id
        LEFT JOIN orders o ON oh.order_id = o.id
        WHERE DATE(oh.created_at) BETWEEN ? AND ?
    ";
    
    $params = [$date_from, $date_to];
    
    if ($action_type) {
        $sql .= " AND oh.action_type = ?";
        $params[] = $action_type;
    }
    
    if ($user_id) {
        $sql .= " AND oh.user_id = ?";
        $params[] = $user_id;
    }
    
    $sql .= " ORDER BY oh.created_at DESC LIMIT ?";
    $params[] = $limit;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $activities = $stmt->fetchAll();
    
    // Get statistics for the period
    $stats_sql = "
        SELECT 
            COUNT(*) as total_actions,
            COUNT(DISTINCT order_id) as orders_affected,
            COUNT(DISTINCT user_id) as active_users,
            COUNT(DISTINCT DATE(created_at)) as active_days
        FROM order_history 
        WHERE DATE(created_at) BETWEEN ? AND ?
    ";
    
    $stats_params = [$date_from, $date_to];
    
    if ($action_type) {
        $stats_sql .= " AND action_type = ?";
        $stats_params[] = $action_type;
    }
    
    if ($user_id) {
        $stats_sql .= " AND user_id = ?";
        $stats_params[] = $user_id;
    }
    
    $stmt = $pdo->prepare($stats_sql);
    $stmt->execute($stats_params);
    $stats = $stmt->fetch();
    
    // Get action type distribution
    $action_stats_sql = "
        SELECT action_type, COUNT(*) as count
        FROM order_history 
        WHERE DATE(created_at) BETWEEN ? AND ?
    ";
    
    $action_params = [$date_from, $date_to];
    
    if ($user_id) {
        $action_stats_sql .= " AND user_id = ?";
        $action_params[] = $user_id;
    }
    
    $action_stats_sql .= " GROUP BY action_type ORDER BY count DESC";
    
    $stmt = $pdo->prepare($action_stats_sql);
    $stmt->execute($action_params);
    $action_stats = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $db_error = $e->getMessage();
}

// Helper functions
function getActionIcon($action_type) {
    $icons = [
        'preview_status_change' => 'fas fa-eye',
        'delivery_date_change' => 'fas fa-calendar-alt',
        'technology_assignment' => 'fas fa-cogs',
        'item_relevance_change' => 'fas fa-toggle-on',
        'item_status_change' => 'fas fa-boxes'
    ];
    return $icons[$action_type] ?? 'fas fa-edit';
}

function getActionColor($action_type) {
    $colors = [
        'preview_status_change' => 'primary',
        'delivery_date_change' => 'warning',
        'technology_assignment' => 'info',
        'item_relevance_change' => 'success',
        'item_status_change' => 'secondary'
    ];
    return $colors[$action_type] ?? 'dark';
}

function formatTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'před chvílí';
    if ($time < 3600) return 'před ' . floor($time/60) . ' min';
    if ($time < 86400) return 'před ' . floor($time/3600) . ' h';
    if ($time < 2592000) return 'před ' . floor($time/86400) . ' dny';
    
    return date('d.m.Y', strtotime($datetime));
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
    <link href="../assets/css/reports.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-server me-2"></i>CIG Realizace
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="../reports/index.php">
                    <i class="fas fa-chart-line me-1"></i>Reporty
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>Odhlásit
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-server me-2"></i><?= $page_title ?></h1>
                    <div class="export-buttons">
                        <button type="button" class="export-btn pdf" onclick="exportReport('pdf')">
                            <i class="fas fa-file-pdf"></i>Export PDF
                        </button>
                        <button type="button" class="export-btn excel" onclick="exportReport('excel')">
                            <i class="fas fa-file-excel"></i>Export Excel
                        </button>
                        <button type="button" class="export-btn csv" onclick="exportReport('csv')">
                            <i class="fas fa-file-csv"></i>Export CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <?php if (isset($db_error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Chyba připojení k databázi: <?= htmlspecialchars($db_error) ?>
        </div>
        <?php else: ?>

        <!-- Filters -->
        <div class="report-filters">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">Od data</label>
                    <input type="date" class="form-control report-filter" id="date_from" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">Do data</label>
                    <input type="date" class="form-control report-filter" id="date_to" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                </div>
                <div class="col-md-2">
                    <label for="action_type" class="form-label">Typ akce</label>
                    <select class="form-select report-filter" id="action_type" name="action_type">
                        <option value="">Všechny</option>
                        <option value="preview_status_change" <?= $action_type === 'preview_status_change' ? 'selected' : '' ?>>Změna stavu náhledu</option>
                        <option value="delivery_date_change" <?= $action_type === 'delivery_date_change' ? 'selected' : '' ?>>Změna termínu</option>
                        <option value="technology_assignment" <?= $action_type === 'technology_assignment' ? 'selected' : '' ?>>Přiřazení technologie</option>
                        <option value="item_relevance_change" <?= $action_type === 'item_relevance_change' ? 'selected' : '' ?>>Relevance položky</option>
                        <option value="item_status_change" <?= $action_type === 'item_status_change' ? 'selected' : '' ?>>Stav zásob</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="user_id" class="form-label">Uživatel</label>
                    <select class="form-select report-filter" id="user_id" name="user_id">
                        <option value="">Všichni</option>
                        <?php foreach ($users as $user): ?>
                        <option value="<?= $user['id'] ?>" <?= $user_id == $user['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($user['full_name']) ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="limit" class="form-label">Limit</label>
                    <select class="form-select report-filter" id="limit" name="limit">
                        <option value="25" <?= $limit === 25 ? 'selected' : '' ?>>25</option>
                        <option value="50" <?= $limit === 50 ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= $limit === 100 ? 'selected' : '' ?>>100</option>
                        <option value="200" <?= $limit === 200 ? 'selected' : '' ?>>200</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>Aplikovat filtry
                    </button>
                    <a href="system_activity.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Zrušit filtry
                    </a>
                </div>
            </form>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4><?= number_format($stats['total_actions'] ?? 0) ?></h4>
                        <p class="mb-0">Celkem akcí</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4><?= number_format($stats['orders_affected'] ?? 0) ?></h4>
                        <p class="mb-0">Upravených objednávek</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4><?= number_format($stats['active_users'] ?? 0) ?></h4>
                        <p class="mb-0">Aktivních uživatelů</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h4><?= number_format($stats['active_days'] ?? 0) ?></h4>
                        <p class="mb-0">Aktivních dní</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>Distribuce typů akcí</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="actionTypeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>Aktivita v čase</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="activityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity List -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>Seznam aktivit</h5>
            </div>
            <div class="card-body">
                <div class="activity-feed" id="activityFeed">
                    <?php if (empty($activities)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Žádné aktivity nebyly nalezeny pro zadané filtry.</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($activities as $activity): ?>
                    <div class="activity-item">
                        <div class="activity-icon <?= getActionColor($activity['action_type']) ?>">
                            <i class="<?= getActionIcon($activity['action_type']) ?>"></i>
                        </div>
                        <div class="activity-content">
                            <p>
                                <strong><?= htmlspecialchars($activity['full_name'] ?? $activity['username']) ?></strong>
                                <?= htmlspecialchars($activity['description']) ?>
                                <?php if ($activity['order_code']): ?>
                                - <a href="../orders/detail.php?id=<?= $activity['order_id'] ?>" class="text-decoration-none">
                                    <?= htmlspecialchars($activity['order_code']) ?>
                                </a>
                                <?php endif; ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="activity-time"><?= formatTimeAgo($activity['created_at']) ?></span>
                                <span class="role-badge <?= $activity['role'] ?>">
                                    <?= htmlspecialchars($activity['role']) ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/reports.js"></script>
    <script>
        // Initialize charts with data
        const actionTypeData = <?= json_encode($action_stats) ?>;
        const activityData = <?= json_encode($activities) ?>;
        
        // Initialize reports page
        if (typeof Reports !== 'undefined') {
            Reports.init();
            
            // Initialize action type chart
            if (actionTypeData.length > 0) {
                const labels = actionTypeData.map(item => {
                    const typeLabels = {
                        'preview_status_change': 'Změna stavu náhledu',
                        'delivery_date_change': 'Změna termínu',
                        'technology_assignment': 'Přiřazení technologie',
                        'item_relevance_change': 'Relevance položky',
                        'item_status_change': 'Stav zásob'
                    };
                    return typeLabels[item.action_type] || item.action_type;
                });
                const values = actionTypeData.map(item => parseInt(item.count));
                
                const ctx = document.getElementById('actionTypeChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: labels,
                            datasets: [{
                                data: values,
                                backgroundColor: [
                                    '#667eea', '#764ba2', '#f093fb', '#f5576c',
                                    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                }
            }
            
            // Initialize activity timeline chart
            if (activityData.length > 0) {
                Reports.initActivityChart(activityData);
            }
        }
    </script>
</body>
</html>
