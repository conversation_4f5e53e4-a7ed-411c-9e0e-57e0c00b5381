<?php
require_once '../includes/session.php';
require_once '../includes/auth_check.php';
require_once '../config/database.php';
require_once '../classes/HistoryLogger.php';

header('Content-Type: application/json');

// Get parameters
$limit = intval($_GET['limit'] ?? 10);
$user_id = intval($_GET['user_id'] ?? 0);
$action_type = trim($_GET['action_type'] ?? '');
$format = $_GET['format'] ?? 'json'; // json or html

try {
    $pdo = getDbConnection();
    
    // Build query
    $sql = "
        SELECT oh.*, u.username, u.full_name, u.role, o.order_code
        FROM order_history oh
        LEFT JOIN users u ON oh.user_id = u.id
        LEFT JOIN orders o ON oh.order_id = o.id
        WHERE 1=1
    ";
    
    $params = [];
    
    // Apply filters
    if ($user_id > 0) {
        $sql .= " AND oh.user_id = ?";
        $params[] = $user_id;
    }
    
    if ($action_type) {
        $sql .= " AND oh.action_type = ?";
        $params[] = $action_type;
    }
    
    // Role-based filtering
    if ($current_user['role'] === 'obchodnik') {
        // Sales rep can only see their own orders
        $sql .= " AND o.sales_rep_id = ?";
        $params[] = $current_user['id'];
    }
    
    $sql .= " ORDER BY oh.created_at DESC LIMIT ?";
    $params[] = $limit;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $activities = $stmt->fetchAll();
    
    if ($format === 'html') {
        // Return HTML for direct insertion
        ob_start();
        
        if (empty($activities)) {
            echo '<div class="text-center py-4">';
            echo '<i class="fas fa-inbox fa-3x text-muted mb-3"></i>';
            echo '<p class="text-muted">Žádné aktivity nebyly nalezeny.</p>';
            echo '</div>';
        } else {
            foreach ($activities as $activity) {
                $icon = getActionIcon($activity['action_type']);
                $color = getActionColor($activity['action_type']);
                $timeAgo = formatTimeAgo($activity['created_at']);
                
                echo '<div class="activity-item">';
                echo '<div class="activity-icon ' . $color . '">';
                echo '<i class="' . $icon . '"></i>';
                echo '</div>';
                echo '<div class="activity-content">';
                echo '<p>';
                echo '<strong>' . htmlspecialchars($activity['full_name'] ?? $activity['username']) . '</strong> ';
                echo htmlspecialchars($activity['description']);
                
                if ($activity['order_code']) {
                    echo ' - <a href="../orders/detail.php?id=' . $activity['order_id'] . '" class="text-decoration-none">';
                    echo htmlspecialchars($activity['order_code']);
                    echo '</a>';
                }
                
                echo '</p>';
                echo '<div class="d-flex justify-content-between align-items-center">';
                echo '<span class="activity-time">' . $timeAgo . '</span>';
                echo '<span class="role-badge ' . $activity['role'] . '">';
                echo htmlspecialchars($activity['role']);
                echo '</span>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
            }
        }
        
        $html = ob_get_clean();
        
        echo json_encode([
            'success' => true,
            'html' => $html,
            'count' => count($activities)
        ]);
        
    } else {
        // Return JSON data
        $formatted_activities = [];
        
        foreach ($activities as $activity) {
            $formatted_activities[] = [
                'id' => $activity['id'],
                'action_type' => $activity['action_type'],
                'description' => $activity['description'],
                'created_at' => $activity['created_at'],
                'order_id' => $activity['order_id'],
                'order_code' => $activity['order_code'],
                'user' => [
                    'id' => $activity['user_id'],
                    'username' => $activity['username'],
                    'full_name' => $activity['full_name'],
                    'role' => $activity['role']
                ],
                'formatted_date' => date('d.m.Y H:i', strtotime($activity['created_at'])),
                'time_ago' => formatTimeAgo($activity['created_at']),
                'icon' => getActionIcon($activity['action_type']),
                'color' => getActionColor($activity['action_type'])
            ];
        }
        
        echo json_encode([
            'success' => true,
            'activities' => $formatted_activities,
            'count' => count($formatted_activities)
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error getting activity feed: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při načítání aktivity',
        'error' => $e->getMessage()
    ]);
}

// Helper functions
function getActionIcon($action_type) {
    $icons = [
        'preview_status_change' => 'fas fa-eye',
        'delivery_date_change' => 'fas fa-calendar-alt',
        'technology_assignment' => 'fas fa-cogs',
        'item_relevance_change' => 'fas fa-toggle-on',
        'item_status_change' => 'fas fa-boxes',
        'order_created' => 'fas fa-plus-circle',
        'order_completed' => 'fas fa-check-circle',
        'user_login' => 'fas fa-sign-in-alt',
        'csv_import' => 'fas fa-file-import'
    ];
    return $icons[$action_type] ?? 'fas fa-edit';
}

function getActionColor($action_type) {
    $colors = [
        'preview_status_change' => 'status-change',
        'delivery_date_change' => 'delivery-date',
        'technology_assignment' => 'technology',
        'item_relevance_change' => 'item-relevance',
        'item_status_change' => 'item-status',
        'order_created' => 'order-created',
        'order_completed' => 'order-completed',
        'user_login' => 'user-action',
        'csv_import' => 'import-action'
    ];
    return $colors[$action_type] ?? 'default';
}

function formatTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'před chvílí';
    if ($time < 3600) return 'před ' . floor($time/60) . ' min';
    if ($time < 86400) return 'před ' . floor($time/3600) . ' h';
    if ($time < 2592000) return 'před ' . floor($time/86400) . ' dny';
    
    return date('d.m.Y H:i', strtotime($datetime));
}
?>
