<?php
/**
 * AJAX endpoint for updating item inventory status
 * CIG Realizace - Phase 05
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and history logger
require_once '../includes/auth_check.php';
require_once '../classes/HistoryLogger.php';

// Check if user is logged in and has permission
$current_user = getCurrentUser();
if (!$current_user || !in_array($current_user['role'], ['admin', 'realizator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Nemáte oprávnění k této akci']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

// Get and validate input
$item_id = intval($_POST['item_id'] ?? 0);
$inventory_status = $_POST['inventory_status'] ?? '';

if (!$item_id) {
    echo json_encode(['success' => false, 'message' => 'Neplatné ID položky']);
    exit;
}

// Validate inventory status
$allowed_statuses = ['not_in_stock', 'ordered', 'in_stock'];
if (!in_array($inventory_status, $allowed_statuses)) {
    echo json_encode(['success' => false, 'message' => 'Neplatný stav zásob']);
    exit;
}

try {
    $pdo = getDbConnection();
    $pdo->beginTransaction();
    
    // Get current item data
    $stmt = $pdo->prepare("
        SELECT oi.*, o.order_code, o.id as order_id
        FROM order_items oi
        LEFT JOIN orders o ON oi.order_id = o.id
        WHERE oi.id = ?
    ");
    $stmt->execute([$item_id]);
    $item = $stmt->fetch();
    
    if (!$item) {
        throw new Exception('Položka nebyla nalezena');
    }
    
    $old_status = $item['inventory_status'];
    
    // Don't update if status is the same
    if ($old_status === $inventory_status) {
        echo json_encode([
            'success' => true,
            'message' => 'Stav zásob nebyl změněn',
            'data' => [
                'item_id' => $item_id,
                'inventory_status' => $inventory_status
            ]
        ]);
        exit;
    }
    
    // Update inventory status
    $stmt = $pdo->prepare("
        UPDATE order_items 
        SET inventory_status = ?, updated_at = ? 
        WHERE id = ?
    ");
    $stmt->execute([$inventory_status, date('Y-m-d H:i:s'), $item_id]);
    
    // Log the change in order history
    $status_labels = [
        'not_in_stock' => 'Není skladem',
        'ordered' => 'Objednáno',
        'in_stock' => 'Skladem'
    ];
    
    $old_label = $status_labels[$old_status] ?? $old_status;
    $new_label = $status_labels[$inventory_status] ?? $inventory_status;
    
    // Log the change using HistoryLogger
    HistoryLogger::logInventoryStatusChange(
        $item['order_id'],
        $current_user['id'],
        $item_id,
        $item['catalog_code'],
        $old_status,
        $inventory_status
    );
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Stav zásob byl úspěšně aktualizován',
        'data' => [
            'item_id' => $item_id,
            'old_status' => $old_status,
            'new_status' => $inventory_status,
            'catalog_code' => $item['catalog_code']
        ],
        'status_label' => $status_labels[$inventory_status]
    ]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error updating inventory status: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při aktualizaci stavu zásob: ' . $e->getMessage()
    ]);
}
?>
