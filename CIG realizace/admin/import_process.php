<?php
// CSV Import Processing
// CIG Realizace - Order Management System

session_start();
require_once '../includes/auth_check.php';
require_once '../config/database.php';
require_once '../classes/CSVImporter.php';

// Check admin access
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../includes/access_denied.php');
    exit;
}

$result = null;
$upload_error = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    try {
        // Check for upload errors
        if ($_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Chyba při nahrávání souboru: ' . $_FILES['csv_file']['error']);
        }
        
        // Validate file type
        $file_info = pathinfo($_FILES['csv_file']['name']);
        $allowed_extensions = ['csv', 'txt'];
        
        if (!in_array(strtolower($file_info['extension']), $allowed_extensions)) {
            throw new Exception('Nepodporovaný formát souboru. Povolené jsou pouze CSV a TXT soubory.');
        }
        
        // Check file size (10MB limit)
        if ($_FILES['csv_file']['size'] > 10 * 1024 * 1024) {
            throw new Exception('Soubor je příliš velký. Maximální velikost je 10 MB.');
        }
        
        $temp_file = $_FILES['csv_file']['tmp_name'];
        $original_filename = $_FILES['csv_file']['name'];
        $preview_only = isset($_POST['preview_only']) && $_POST['preview_only'] === '1';
        
        // Initialize importer
        $pdo = getDbConnection();
        $importer = new CSVImporter($pdo);
        
        // Process import
        $start_time = microtime(true);
        $result = $importer->importCSV($temp_file, $preview_only);
        $processing_time = round(microtime(true) - $start_time, 2);
        
        // Log import attempt
        if (!$preview_only) {
            $log_stmt = $pdo->prepare("
                INSERT INTO import_logs (
                    filename, user_id, status, 
                    total_rows, orders_created, items_created, 
                    errors_count, warnings_count, processing_time,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $log_stmt->execute([
                $original_filename,
                $_SESSION['user_id'],
                $result['success'] ? 'success' : 'error',
                $result['stats']['total_rows'],
                $result['stats']['orders_created'],
                $result['stats']['items_created'],
                count($result['errors']),
                count($result['warnings']),
                $processing_time
            ]);
        }
        
        $result['processing_time'] = $processing_time;
        $result['filename'] = $original_filename;
        $result['preview_only'] = $preview_only;
        
    } catch (Exception $e) {
        $upload_error = $e->getMessage();
    }
} else {
    header('Location: import.php');
    exit;
}

$page_title = $result['preview_only'] ? 'Náhled importu CSV' : 'Výsledek importu CSV';
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i><?= htmlspecialchars($_SESSION['full_name']) ?>
                </span>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Odhlásit
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>Administrace</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="../dashboard.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a href="users.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-users me-2"></i>Správa uživatelů
                        </a>
                        <a href="import.php" class="list-group-item list-group-item-action active">
                            <i class="fas fa-file-import me-2"></i>Import CSV
                        </a>
                        <a href="import_history.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i>Historie importů
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h4>
                            <?php if ($result && $result['success']): ?>
                                <i class="fas fa-check-circle text-success me-2"></i>
                            <?php else: ?>
                                <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                            <?php endif; ?>
                            <?= $page_title ?>
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <?php if ($upload_error): ?>
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-circle me-2"></i>Chyba při zpracování</h5>
                            <p class="mb-0"><?= htmlspecialchars($upload_error) ?></p>
                        </div>
                        
                        <?php elseif ($result): ?>
                        
                        <!-- Import Summary -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card <?= $result['success'] ? 'border-success' : 'border-danger' ?>">
                                    <div class="card-body">
                                        <h6>Souhrn zpracování</h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><strong>Soubor:</strong> <?= htmlspecialchars($result['filename']) ?></li>
                                            <li><strong>Celkem řádků:</strong> <?= $result['stats']['total_rows'] ?></li>
                                            <li><strong>Zpracováno objednávek:</strong> <?= $result['stats']['orders_created'] ?></li>
                                            <li><strong>Vytvořeno položek:</strong> <?= $result['stats']['items_created'] ?></li>
                                            <li><strong>Doba zpracování:</strong> <?= $result['processing_time'] ?>s</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Statistiky</h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><strong>Chyby:</strong> 
                                                <span class="badge bg-danger"><?= count($result['errors']) ?></span>
                                            </li>
                                            <li><strong>Varování:</strong> 
                                                <span class="badge bg-warning"><?= count($result['warnings']) ?></span>
                                            </li>
                                            <li><strong>Status:</strong> 
                                                <?php if ($result['success']): ?>
                                                    <span class="badge bg-success">Úspěch</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Chyba</span>
                                                <?php endif; ?>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Mode Notice -->
                        <?php if ($result['preview_only']): ?>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-eye me-2"></i>Režim náhledu</h6>
                            <p class="mb-0">Data nebyla uložena do databáze. Pro skutečný import odešlete formulář bez zaškrtnutí "Pouze náhled".</p>
                        </div>
                        <?php endif; ?>

                        <!-- Errors -->
                        <?php if (!empty($result['errors'])): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-circle me-2"></i>Chyby při zpracování</h6>
                            <ul class="mb-0">
                                <?php foreach ($result['errors'] as $error): ?>
                                <li><?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <!-- Warnings -->
                        <?php if (!empty($result['warnings'])): ?>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Varování</h6>
                            <ul class="mb-0">
                                <?php foreach (array_slice($result['warnings'], 0, 10) as $warning): ?>
                                <li><?= htmlspecialchars($warning) ?></li>
                                <?php endforeach; ?>
                                <?php if (count($result['warnings']) > 10): ?>
                                <li><em>... a dalších <?= count($result['warnings']) - 10 ?> varování</em></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <!-- Preview Data -->
                        <?php if ($result['preview_only'] && !empty($result['preview_data'])): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6><i class="fas fa-eye me-2"></i>Náhled dat (prvních 5 objednávek)</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($result['preview_data'] as $order): ?>
                                <div class="border rounded p-3 mb-3">
                                    <h6><?= htmlspecialchars($order['order_code']) ?> 
                                        <small class="text-muted">(<?= htmlspecialchars($order['sales_rep']) ?>)</small>
                                    </h6>
                                    <p class="mb-2"><strong>Datum objednávky:</strong> <?= $order['order_date'] ?: 'Neuvedeno' ?></p>
                                    <p class="mb-2"><strong>Počet položek:</strong> <?= count($order['items']) ?></p>
                                    
                                    <?php if (!empty($order['items'])): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Katalog</th>
                                                    <th>Množství</th>
                                                    <th>Stav zásob</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach (array_slice($order['items'], 0, 3) as $item): ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($item['catalog']) ?: '<em>Prázdné</em>' ?></td>
                                                    <td><?= $item['quantity'] ?></td>
                                                    <td>
                                                        <?php
                                                        $status_labels = [
                                                            'in_stock' => '<span class="badge bg-success">Na skladě</span>',
                                                            'ordered' => '<span class="badge bg-warning">Objednáno</span>',
                                                            'not_in_stock' => '<span class="badge bg-danger">Není na skladě</span>'
                                                        ];
                                                        echo $status_labels[$item['inventory_status']] ?? $item['inventory_status'];
                                                        ?>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                                <?php if (count($order['items']) > 3): ?>
                                                <tr>
                                                    <td colspan="3"><em>... a dalších <?= count($order['items']) - 3 ?> položek</em></td>
                                                </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php endif; ?>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="import.php" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-1"></i>Zpět na import
                            </a>
                            <?php if ($result && $result['success'] && !$result['preview_only']): ?>
                            <a href="../dashboard.php" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt me-1"></i>Přejít na dashboard
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
