<?php
/**
 * AJAX endpoint for getting order history
 * CIG Realizace - Phase 06
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and history logger
require_once '../includes/auth_check.php';
require_once '../classes/HistoryLogger.php';

// Check if user is logged in
$current_user = getCurrentUser();
if (!$current_user) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášeni']);
    exit;
}

// Check if request is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

// Get and validate input
$order_id = intval($_GET['order_id'] ?? 0);
$limit = intval($_GET['limit'] ?? 50);
$action_type = trim($_GET['action_type'] ?? '');

if (!$order_id) {
    echo json_encode(['success' => false, 'message' => 'Neplatné ID objednávky']);
    exit;
}

try {
    $pdo = getDbConnection();
    
    // Check if user has access to this order
    $stmt = $pdo->prepare("
        SELECT o.*, u.username as sales_rep_username
        FROM orders o
        LEFT JOIN users u ON o.sales_rep_id = u.id
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Objednávka nebyla nalezena');
    }
    
    // Check permissions - admin sees all, sales rep sees only their orders
    if ($current_user['role'] !== 'admin' && 
        $current_user['role'] !== 'obchodnik' && 
        $order['sales_rep_id'] != $current_user['id']) {
        throw new Exception('Nemáte oprávnění k zobrazení historie této objednávky');
    }
    
    // Get order history
    $history = HistoryLogger::getOrderHistory($order_id, $limit, $action_type ?: null);
    
    // Format history for display
    $formatted_history = [];
    foreach ($history as $entry) {
        $formatted_entry = [
            'id' => $entry['id'],
            'action_type' => $entry['action_type'],
            'old_value' => $entry['old_value'],
            'new_value' => $entry['new_value'],
            'description' => $entry['description'],
            'created_at' => $entry['created_at'],
            'user' => [
                'id' => $entry['user_id'],
                'username' => $entry['username'],
                'full_name' => $entry['full_name'],
                'role' => $entry['role']
            ],
            'formatted_date' => date('d.m.Y H:i', strtotime($entry['created_at'])),
            'time_ago' => formatTimeAgo($entry['created_at']),
            'icon' => getActionIcon($entry['action_type']),
            'color' => getActionColor($entry['action_type'])
        ];
        
        // Parse additional data if available
        if (!empty($entry['additional_data'])) {
            $additional_data = json_decode($entry['additional_data'], true);
            if ($additional_data) {
                $formatted_entry['additional_data'] = $additional_data;
            }
        }
        
        $formatted_history[] = $formatted_entry;
    }
    
    echo json_encode([
        'success' => true,
        'order' => [
            'id' => $order['id'],
            'order_code' => $order['order_code'],
            'sales_rep' => $order['sales_rep_username']
        ],
        'history' => $formatted_history,
        'total_entries' => count($formatted_history)
    ]);
    
} catch (Exception $e) {
    error_log("Error getting order history: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Format time ago in Czech
 */
function formatTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'Právě teď';
    if ($time < 3600) return floor($time/60) . ' min';
    if ($time < 86400) return floor($time/3600) . ' hod';
    if ($time < 2592000) return floor($time/86400) . ' dní';
    if ($time < 31536000) return floor($time/2592000) . ' měs';
    
    return floor($time/31536000) . ' let';
}

/**
 * Get icon for action type
 */
function getActionIcon($actionType) {
    $icons = [
        'preview_status_change' => 'fas fa-eye',
        'delivery_date_change' => 'fas fa-calendar-alt',
        'technology_assignment' => 'fas fa-cogs',
        'item_relevance_change' => 'fas fa-toggle-on',
        'inventory_status_change' => 'fas fa-boxes',
        'order_completion' => 'fas fa-check-circle',
        'order_created' => 'fas fa-plus-circle',
        'order_updated' => 'fas fa-edit'
    ];
    
    return $icons[$actionType] ?? 'fas fa-info-circle';
}

/**
 * Get color for action type
 */
function getActionColor($actionType) {
    $colors = [
        'preview_status_change' => 'primary',
        'delivery_date_change' => 'warning',
        'technology_assignment' => 'info',
        'item_relevance_change' => 'secondary',
        'inventory_status_change' => 'success',
        'order_completion' => 'success',
        'order_created' => 'success',
        'order_updated' => 'info'
    ];
    
    return $colors[$actionType] ?? 'secondary';
}
?>
