# Vlast<PERSON><PERSON> sekce "VLASTNÍ MODULY" pro PrestaShop

Univerzální balíček pro vytvoření vlastní sekce v administraci PrestaShop, kam se automaticky řadí všechny vaše custom moduly.

## 🎯 Co tento balíček obsahuje

- **CustomModulesTabManager.php** - Univerzální třída pro správu tabů
- **template-new-module.php** - Template pro nové moduly
- **add-to-existing-module.php** - Script pro přidání do existujících modulů
- **README.md** - Tato dokumentace

## 📋 Výsledek

Po implementaci bude struktura menu vypadat takto:

```
KONFIGURACE
├── Nastavení eshopu
└── Nástroje

VLASTNÍ MODULY              ← NOVÁ SEKCE
├── Katalogy               
├── Váš modul 1
├── Váš modul 2
└── ...

ADVANCE SEO
├── SEO Configuration
└── ...
```

## 🚀 Použití pro NOVÉ moduly

### 1. Zkopírujte soubory
```
vas_novy_modul/
├── classes/
│   └── CustomModulesTabManager.php  ← Zkopírovat
├── vas_novy_modul.php
└── ...
```

### 2. Použijte template
1. Zkopírujte `template-new-module.php` a přejmenujte na `vas_modul.php`
2. Nahraďte všechny výskyty:
   - `NAZEV_MODULU` → `VAS_MODUL` (velkými písmeny)
   - `nazev_modulu` → `vas_modul` (malými písmeny)
   - `AdminNAZEV_MODULU` → `AdminVasModul`

### 3. Vytvořte controller
Vytvořte soubor `controllers/admin/AdminVasModul.php`:

```php
<?php
class AdminVasModulController extends ModuleAdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->bootstrap = true;
    }

    public function initContent()
    {
        parent::initContent();
        // Váš obsah administrace
    }
}
```

## 🔄 Použití pro EXISTUJÍCÍ moduly

### Metoda 1: Automatický script

1. **Zkopírujte** `CustomModulesTabManager.php` do `modules/vas_modul/classes/`

2. **Spusťte script:**
   ```
   modules/vas_modul/add-to-existing-module.php?module=vas_modul&controller=AdminVasModul
   ```

3. **Následujte instrukce** na stránce

### Metoda 2: Ruční úprava

1. **Zkopírujte** `CustomModulesTabManager.php` do `modules/vas_modul/classes/`

2. **Upravte** metody v hlavním souboru modulu:

```php
private function createTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::createModuleTab('AdminVasModul', 'Váš modul', $this->name);
}

private function removeTab()
{
    require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
    return CustomModulesTabManager::removeModuleTab('AdminVasModul');
}
```

3. **Přesuňte existující tab:**
```php
// Jednorázově spusťte pro přesun existujícího tabu
CustomModulesTabManager::moveTabToCustomModules('AdminVasModul');
```

## 🔧 API Reference

### CustomModulesTabManager

#### Hlavní metody:

```php
// Vytvoří tab v sekci "Vlastní moduly"
CustomModulesTabManager::createModuleTab($class_name, $tab_name, $module_name);

// Odstraní tab a vyčistí prázdnou sekci
CustomModulesTabManager::removeModuleTab($class_name);

// Přesune existující tab do vlastní sekce
CustomModulesTabManager::moveTabToCustomModules($class_name);

// Zkontroluje existenci sekce
CustomModulesTabManager::customModulesSectionExists();
```

#### Parametry:

- **$class_name** - Název controller třídy (např. `'AdminKatalogy'`)
- **$tab_name** - Zobrazovaný název (např. `'Katalogy'`)
- **$module_name** - Název modulu (např. `'katalogy'`)

## ✅ Výhody

### Organizace
- ✅ Všechny custom moduly na jednom místě
- ✅ Čistší struktura administrace
- ✅ Oddělení od standardních PrestaShop modulů

### Automatizace
- ✅ Automatické vytváření sekce při instalaci prvního modulu
- ✅ Automatické mazání prázdné sekce při odinstalaci posledního modulu
- ✅ Žádná ruční správa potřebná

### Univerzálnost
- ✅ Jedna implementace pro všechny moduly
- ✅ Snadná integrace do existujících modulů
- ✅ Kompatibilita s PrestaShop 1.7+ a 8.x

## 🛠️ Troubleshooting

### Sekce se nevytváří
1. Zkontrolujte oprávnění k databázi
2. Ověřte, že třída `CustomModulesTabManager` je správně načtena
3. Zkontrolujte logy PrestaShop

### Tab se nezobrazuje
1. Vyčistěte cache administrace
2. Zkontrolujte, že controller třída existuje
3. Ověřte oprávnění uživatele

### Sekce je na špatném místě
1. Spusťte `add-to-existing-module.php` script
2. Použijte akci "Opravit pozici"

## 📞 Podpora

Pro řešení problémů použijte:
- `add-to-existing-module.php` - Diagnostika a oprava
- Zkontrolujte logy PrestaShop v `var/logs/`

## 📄 Licence

MIT License - můžete volně používat a upravovat.
