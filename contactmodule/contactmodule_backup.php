<?php
/**
 * Contact Module for PrestaShop 8.2.0
 * Simple version for easier installation
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class ContactModule extends Module
{
    public function __construct()
    {
        $this->name = 'contactmodule';
        $this->tab = 'front_office_features';
        $this->version = '1.0.0';
        $this->author = '<PERSON><PERSON><PERSON>';
        $this->need_instance = 0;
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = 'Contact Page Module';
        $this->description = 'Module pro vytvoření kontaktní stránky';
        $this->confirmUninstall = 'Opravdu chcete odinstalovat tento modul?';
    }

    public function install()
    {
        return parent::install() && 
               $this->registerHook('actionFrontControllerSetMedia') &&
               $this->createDefaultConfig();
    }

    public function uninstall()
    {
        return $this->deleteConfig() && parent::uninstall();
    }

    private function createDefaultConfig()
    {
        Configuration::updateValue('CONTACT_COMPANY_NAME', 'Vaše firma s.r.o.');
        Configuration::updateValue('CONTACT_ADDRESS', 'Ulice 123<br>Praha 1, 110 00');
        Configuration::updateValue('CONTACT_PHONE', '+*********** 789');
        Configuration::updateValue('CONTACT_EMAIL', '<EMAIL>');
        Configuration::updateValue('CONTACT_MAP_EMBED', '');
        Configuration::updateValue('CONTACT_BANK_INFO', 'Účet: *********/0100');
        
        $representatives = [
            [
                'name' => 'Jan Novák',
                'position' => 'Obchodní ředitel',
                'phone' => '+*********** 001',
                'email' => '<EMAIL>',
                'region' => 'Praha a střední Čechy'
            ],
            [
                'name' => 'Petra Svobodová',
                'position' => 'Obchodní zástupce',
                'phone' => '+*********** 002',
                'email' => '<EMAIL>',
                'region' => 'Brno a jižní Morava'
            ]
        ];
        
        Configuration::updateValue('CONTACT_REPRESENTATIVES', json_encode($representatives));
        return true;
    }

    private function deleteConfig()
    {
        Configuration::deleteByName('CONTACT_COMPANY_NAME');
        Configuration::deleteByName('CONTACT_ADDRESS');
        Configuration::deleteByName('CONTACT_PHONE');
        Configuration::deleteByName('CONTACT_EMAIL');
        Configuration::deleteByName('CONTACT_MAP_EMBED');
        Configuration::deleteByName('CONTACT_BANK_INFO');
        Configuration::deleteByName('CONTACT_REPRESENTATIVES');
        return true;
    }

    public function hookActionFrontControllerSetMedia()
    {
        $this->context->controller->addCSS($this->_path . 'views/css/contactmodule.css');
    }

    public function getContent()
    {
        $output = '';

        if (Tools::isSubmit('submitContactModule')) {
            Configuration::updateValue('CONTACT_COMPANY_NAME', Tools::getValue('company_name'));
            Configuration::updateValue('CONTACT_ADDRESS', Tools::getValue('address'));
            Configuration::updateValue('CONTACT_PHONE', Tools::getValue('phone'));
            Configuration::updateValue('CONTACT_EMAIL', Tools::getValue('email'));
            Configuration::updateValue('CONTACT_MAP_EMBED', Tools::getValue('map_embed'));
            Configuration::updateValue('CONTACT_BANK_INFO', Tools::getValue('bank_info'));
            
            $output .= $this->displayConfirmation('Nastavení bylo uloženo.');
        }

        $output .= $this->displayForm();
        return $output;
    }

    private function displayForm()
    {
        $fields_form = [
            'form' => [
                'legend' => [
                    'title' => 'Nastavení kontaktní stránky',
                    'icon' => 'icon-cogs'
                ],
                'input' => [
                    [
                        'type' => 'text',
                        'label' => 'Název společnosti',
                        'name' => 'company_name',
                        'size' => 50,
                        'required' => true
                    ],
                    [
                        'type' => 'textarea',
                        'label' => 'Adresa',
                        'name' => 'address',
                        'rows' => 3,
                        'cols' => 60
                    ],
                    [
                        'type' => 'text',
                        'label' => 'Telefon',
                        'name' => 'phone',
                        'size' => 30
                    ],
                    [
                        'type' => 'text',
                        'label' => 'Email',
                        'name' => 'email',
                        'size' => 50
                    ],
                    [
                        'type' => 'textarea',
                        'label' => 'Mapa (embed kód)',
                        'name' => 'map_embed',
                        'rows' => 5,
                        'cols' => 80,
                        'desc' => 'Vložte iframe kód z Google Maps'
                    ],
                    [
                        'type' => 'textarea',
                        'label' => 'Bankovní spojení',
                        'name' => 'bank_info',
                        'rows' => 3,
                        'cols' => 60
                    ]
                ],
                'submit' => [
                    'title' => 'Uložit',
                    'class' => 'btn btn-default pull-right'
                ]
            ]
        ];

        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->default_form_language = (int)Configuration::get('PS_LANG_DEFAULT');
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);
        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submitContactModule';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false)
            . '&configure=' . $this->name . '&tab_module=' . $this->tab . '&module_name=' . $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $helper->fields_value = [
            'company_name' => Configuration::get('CONTACT_COMPANY_NAME'),
            'address' => Configuration::get('CONTACT_ADDRESS'),
            'phone' => Configuration::get('CONTACT_PHONE'),
            'email' => Configuration::get('CONTACT_EMAIL'),
            'map_embed' => Configuration::get('CONTACT_MAP_EMBED'),
            'bank_info' => Configuration::get('CONTACT_BANK_INFO')
        ];

        return $helper->generateForm([$fields_form]);
    }

    public function getContactPageContent()
    {
        $representatives = json_decode(Configuration::get('CONTACT_REPRESENTATIVES'), true);
        if (!$representatives) {
            $representatives = [];
        }

        $this->context->smarty->assign([
            'company_name' => Configuration::get('CONTACT_COMPANY_NAME'),
            'address' => Configuration::get('CONTACT_ADDRESS'),
            'phone' => Configuration::get('CONTACT_PHONE'),
            'email' => Configuration::get('CONTACT_EMAIL'),
            'map_embed' => Configuration::get('CONTACT_MAP_EMBED'),
            'bank_info' => Configuration::get('CONTACT_BANK_INFO'),
            'representatives' => $representatives
        ]);

        return $this->context->smarty->fetch($this->local_path . 'views/templates/front/contact.tpl');
    }

    public function hookDisplayFooter()
    {
        // Přidá JavaScript pro detekci shortcode [contactmodule]
        $content = addslashes($this->getContactPageContent());
        $js = '
        <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Najde všechny elementy obsahující shortcode
            var elements = document.querySelectorAll("*");
            elements.forEach(function(element) {
                if (element.innerHTML && element.innerHTML.includes("[contactmodule]")) {
                    // Nahradí shortcode obsahem modulu
                    element.innerHTML = element.innerHTML.replace("[contactmodule]", "' . $content . '");
                }
            });
        });
        </script>';
        return $js;
    }

    // Statická metoda pro snadné volání z šablon
    public static function getContactContent()
    {
        $module = Module::getInstanceByName('contactmodule');
        if ($module && $module->active) {
            return $module->getContactPageContent();
        }
        return '';
    }
}
