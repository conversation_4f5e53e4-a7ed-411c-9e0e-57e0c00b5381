<?php
/**
 * Manuální test uložení mapy
 */

require_once(dirname(__FILE__).'/../../config/config.inc.php');

echo "<h2>Manuální test uložení mapy</h2>";

$test_map = '<iframe style="border:none" src="https://mapy.com/s/nefocevema" width="400" height="280" frameborder="0"></iframe>';

echo "<h3>Test 1: P<PERSON>í<PERSON> ul<PERSON></h3>";
$result1 = Configuration::updateValue('CONTACT_MAP_EMBED', $test_map);
echo "<p>Výsledek uložení: " . ($result1 ? 'ÚSPĚCH' : 'CHYBA') . "</p>";

$saved1 = Configuration::get('CONTACT_MAP_EMBED');
echo "<p>Načteno zpět: " . (empty($saved1) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen($saved1) . ' znaků)') . "</p>";

if (!empty($saved1)) {
    echo "<h4>Obsah:</h4>";
    echo "<textarea style='width: 100%; height: 100px;'>" . htmlspecialchars($saved1) . "</textarea>";
}

echo "<h3>Test 2: Smazání a znovu uložení</h3>";
Configuration::deleteByName('CONTACT_MAP_EMBED');
$result2 = Configuration::updateValue('CONTACT_MAP_EMBED', $test_map);
echo "<p>Výsledek uložení: " . ($result2 ? 'ÚSPĚCH' : 'CHYBA') . "</p>";

$saved2 = Configuration::get('CONTACT_MAP_EMBED');
echo "<p>Načteno zpět: " . (empty($saved2) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen($saved2) . ' znaků)') . "</p>";

echo "<h3>Test 3: Prázdný string</h3>";
$result3 = Configuration::updateValue('CONTACT_MAP_EMBED', '');
echo "<p>Výsledek uložení prázdného stringu: " . ($result3 ? 'ÚSPĚCH' : 'CHYBA') . "</p>";

$saved3 = Configuration::get('CONTACT_MAP_EMBED');
echo "<p>Načteno zpět: " . (empty($saved3) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen($saved3) . ' znaků)') . "</p>";

echo "<h3>Test 4: Znovu uložení mapy</h3>";
$result4 = Configuration::updateValue('CONTACT_MAP_EMBED', $test_map);
echo "<p>Výsledek uložení: " . ($result4 ? 'ÚSPĚCH' : 'CHYBA') . "</p>";

$saved4 = Configuration::get('CONTACT_MAP_EMBED');
echo "<p>Načteno zpět: " . (empty($saved4) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen($saved4) . ' znaků)') . "</p>";

echo "<h3>Aktuální stav všech konfigurací:</h3>";
echo "<ul>";
echo "<li>CONTACT_COMPANY_NAME: " . Configuration::get('CONTACT_COMPANY_NAME') . "</li>";
echo "<li>CONTACT_MAP_EMBED: " . (empty(Configuration::get('CONTACT_MAP_EMBED')) ? 'PRÁZDNÉ' : 'Uloženo (' . strlen(Configuration::get('CONTACT_MAP_EMBED')) . ' znaků)') . "</li>";
echo "</ul>";
?>
