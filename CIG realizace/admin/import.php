<?php
// CSV Import Interface
// CIG Realizace - Order Management System

session_start();
require_once '../includes/auth_check.php';
require_once '../config/database.php';

// Check admin access
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../includes/access_denied.php');
    exit;
}

$page_title = 'Import CSV dat';
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i><?= htmlspecialchars($_SESSION['full_name']) ?>
                </span>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Odhlásit
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>Administrace</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="../dashboard.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a href="users.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-users me-2"></i>Správa uživatelů
                        </a>
                        <a href="import.php" class="list-group-item list-group-item-action active">
                            <i class="fas fa-file-import me-2"></i>Import CSV
                        </a>
                        <a href="import_history.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i>Historie importů
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-file-import me-2"></i><?= $page_title ?></h4>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Informace o importu</h6>
                            <ul class="mb-0">
                                <li>Podporované formáty: CSV soubory s oddělovačem středník (;)</li>
                                <li>Kódování: UTF-8, Windows-1250, ISO-8859-2</li>
                                <li>Maximální velikost souboru: 10 MB</li>
                                <li>Duplicitní objednávky budou přeskočeny</li>
                            </ul>
                        </div>

                        <form id="importForm" action="import_process.php" method="POST" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">
                                            <i class="fas fa-file-csv me-1"></i>Vyberte CSV soubor
                                        </label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" 
                                               accept=".csv,.txt" required>
                                        <div class="form-text">
                                            Vyberte CSV soubor s objednávkami pro import
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Možnosti importu</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="preview_only" name="preview_only" value="1">
                                            <label class="form-check-label" for="preview_only">
                                                Pouze náhled (neukládat do databáze)
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <h6>Očekávaná struktura CSV:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Číslo dokladu</th>
                                                <th>Katalog</th>
                                                <th>Množ. hlav. jedn.</th>
                                                <th>Datum vystavení</th>
                                                <th>Datum vystavení OV</th>
                                                <th>Datum vytvoření DLP</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="text-muted">
                                                <td>25VP-00001</td>
                                                <td>13030683-03</td>
                                                <td>1500,0000</td>
                                                <td>02.01.2025</td>
                                                <td>03.01.2025</td>
                                                <td>06.01.2025</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="mb-3">
                                <h6>Mapování obchodníků:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><strong>VP</strong> → Vláďa</li>
                                            <li><strong>J</strong> → Jirka</li>
                                            <li><strong>NK</strong> → Nikol</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><strong>MR</strong> → Mirka</li>
                                            <li><strong>D</strong> → Daniela</li>
                                            <li><strong>CI</strong> → Czech Image</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="button" class="btn btn-secondary me-md-2" onclick="window.history.back()">
                                    <i class="fas fa-arrow-left me-1"></i>Zpět
                                </button>
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="fas fa-upload me-1"></i>Nahrát a zpracovat
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Recent imports -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>Poslední importy</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $pdo = getDbConnection();
                            $stmt = $pdo->query("
                                SELECT * FROM import_logs 
                                ORDER BY created_at DESC 
                                LIMIT 5
                            ");
                            $recent_imports = $stmt->fetchAll();
                            
                            if ($recent_imports): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Datum</th>
                                            <th>Soubor</th>
                                            <th>Objednávky</th>
                                            <th>Položky</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_imports as $import): ?>
                                        <tr>
                                            <td><?= date('d.m.Y H:i', strtotime($import['created_at'])) ?></td>
                                            <td><?= htmlspecialchars($import['filename']) ?></td>
                                            <td><?= $import['orders_created'] ?></td>
                                            <td><?= $import['items_created'] ?></td>
                                            <td>
                                                <?php if ($import['status'] === 'success'): ?>
                                                    <span class="badge bg-success">Úspěch</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Chyba</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <p class="text-muted">Zatím nebyly provedeny žádné importy.</p>
                            <?php endif;
                        } catch (Exception $e) {
                            echo '<p class="text-danger">Chyba při načítání historie importů.</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('importForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const fileInput = document.getElementById('csv_file');
            
            if (!fileInput.files.length) {
                e.preventDefault();
                alert('Prosím vyberte CSV soubor');
                return;
            }
            
            // Check file size (10MB limit)
            if (fileInput.files[0].size > 10 * 1024 * 1024) {
                e.preventDefault();
                alert('Soubor je příliš velký. Maximální velikost je 10 MB.');
                return;
            }
            
            // Disable submit button and show loading
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Zpracovávám...';
        });
    </script>
</body>
</html>
