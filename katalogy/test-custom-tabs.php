<?php
/**
 * Test script pro ověření funkcionality vlastních tabů
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

echo "<h1>Test funkcionality vlastních tabů</h1>";

// Načti CustomModulesTabManager
require_once(dirname(__FILE__) . '/classes/CustomModulesTabManager.php');

echo "<h2>1. Test existence třídy CustomModulesTabManager</h2>";
if (class_exists('CustomModulesTabManager')) {
    echo "<p style='color: green;'>✅ Třída CustomModulesTabManager existuje</p>";
} else {
    echo "<p style='color: red;'>❌ Třída CustomModulesTabManager neexistuje</p>";
    exit;
}

echo "<h2>2. Test existence sekce 'Vlastní moduly'</h2>";
$custom_section_exists = CustomModulesTabManager::customModulesSectionExists();
if ($custom_section_exists) {
    echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' existuje</p>";

    $custom_modules_id = Tab::getIdFromClassName('AdminCustomModules');
    echo "<p>ID sekce: $custom_modules_id</p>";

    // Zkontroluj pozici sekce
    $sql = 'SELECT position, id_parent FROM `' . _DB_PREFIX_ . 'tab` WHERE class_name = "AdminCustomModules"';
    $tab_info = Db::getInstance()->getRow($sql);
    if ($tab_info) {
        echo "<p>Pozice: {$tab_info['position']}, Parent ID: {$tab_info['id_parent']}</p>";
        if ($tab_info['id_parent'] == 0) {
            echo "<p style='color: green;'>✅ Sekce je správně umístěna jako top-level sekce</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Sekce není top-level (parent: {$tab_info['id_parent']})</p>";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ Sekce 'Vlastní moduly' neexistuje</p>";
}

echo "<h2>3. Test tabu Katalogy</h2>";
$katalogy_tab_id = Tab::getIdFromClassName('AdminKatalogy');
if ($katalogy_tab_id) {
    echo "<p style='color: green;'>✅ Tab AdminKatalogy existuje (ID: $katalogy_tab_id)</p>";
    
    // Získej informace o tabu
    $tab_info = CustomModulesTabManager::getTabInfo('AdminKatalogy');
    if ($tab_info) {
        echo "<p><strong>Název:</strong> " . $tab_info['name'] . "</p>";
        echo "<p><strong>Parent ID:</strong> " . $tab_info['id_parent'] . "</p>";
        echo "<p><strong>Modul:</strong> " . $tab_info['module'] . "</p>";
        echo "<p><strong>Aktivní:</strong> " . ($tab_info['active'] ? 'Ano' : 'Ne') . "</p>";
        
        // Zkontroluj, zda je v sekci "Vlastní moduly"
        if ($custom_section_exists) {
            $custom_modules_id = Tab::getIdFromClassName('AdminCustomModules');
            if ($tab_info['id_parent'] == $custom_modules_id) {
                echo "<p style='color: green;'>✅ Tab Katalogy je správně umístěn v sekci 'Vlastní moduly'</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Tab Katalogy NENÍ v sekci 'Vlastní moduly' (parent: {$tab_info['id_parent']})</p>";
            }
        }
    }
} else {
    echo "<p style='color: red;'>❌ Tab AdminKatalogy neexistuje</p>";
}

echo "<h2>4. Seznam všech modulů v sekci 'Vlastní moduly'</h2>";
if ($custom_section_exists) {
    $custom_modules = CustomModulesTabManager::getCustomModulesTabs();
    if (!empty($custom_modules)) {
        echo "<ul>";
        foreach ($custom_modules as $module_tab) {
            echo "<li><strong>{$module_tab['name']}</strong> ({$module_tab['class_name']})";
            if ($module_tab['module']) {
                echo " - modul: {$module_tab['module']}";
            }
            echo "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Žádné moduly v sekci 'Vlastní moduly'</p>";
    }
} else {
    echo "<p>Sekce 'Vlastní moduly' neexistuje</p>";
}

echo "<h2>5. Test metod CustomModulesTabManager</h2>";

// Test createOrGetCustomModulesTab
try {
    $parent_id = CustomModulesTabManager::createOrGetCustomModulesTab();
    echo "<p style='color: green;'>✅ createOrGetCustomModulesTab() vrátilo ID: $parent_id</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Chyba v createOrGetCustomModulesTab(): " . $e->getMessage() . "</p>";
}

echo "<h2>6. Akce</h2>";
echo "<p><a href='manage-custom-tabs.php' style='background: #007cba; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🔧 Správa tabů</a></p>";

if (!$custom_section_exists) {
    echo "<p><a href='?action=create_section' style='background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>➕ Vytvořit sekci 'Vlastní moduly'</a></p>";
}

if ($katalogy_tab_id && $custom_section_exists) {
    $custom_modules_id = Tab::getIdFromClassName('AdminCustomModules');
    $tab_info = CustomModulesTabManager::getTabInfo('AdminKatalogy');
    if ($tab_info && $tab_info['id_parent'] != $custom_modules_id) {
        echo "<p><a href='?action=move_katalogy' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; border-radius: 3px;'>🔄 Přesunout Katalogy do vlastní sekce</a></p>";
    }
}

// Zpracování akcí
if (isset($_GET['action'])) {
    echo "<hr>";
    
    switch ($_GET['action']) {
        case 'create_section':
            echo "<h3>Vytváření sekce 'Vlastní moduly'</h3>";
            $result = CustomModulesTabManager::createOrGetCustomModulesTab();
            if ($result) {
                echo "<p style='color: green;'>✅ Sekce vytvořena s ID: $result</p>";
                echo "<p><a href='?'>🔄 Obnovit test</a></p>";
            } else {
                echo "<p style='color: red;'>❌ Chyba při vytváření sekce</p>";
            }
            break;
            
        case 'move_katalogy':
            echo "<h3>Přesun tabu Katalogy</h3>";
            $result = CustomModulesTabManager::moveTabToCustomModules('AdminKatalogy');
            if ($result) {
                echo "<p style='color: green;'>✅ Tab Katalogy přesunut do vlastní sekce</p>";
                echo "<p><a href='?'>🔄 Obnovit test</a></p>";
            } else {
                echo "<p style='color: red;'>❌ Chyba při přesunu tabu</p>";
            }
            break;
    }
}

echo "<hr>";
echo "<p><small>Script: " . __FILE__ . "</small></p>";
?>
