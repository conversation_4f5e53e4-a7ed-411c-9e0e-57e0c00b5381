=== PRESTASHOP POSITION UPDATE ===
POST: Array
(
    [katalog] => Array
        (
            [0] => tr_2_21_2
            [1] => tr_2_16_3
            [2] => tr_2_17_4
            [3] => tr_2_18_5
            [4] => tr_2_19_6
            [5] => tr_2_14_1
        )

    [action] => updatePositions
    [id] => 14
    [way] => 1
    [ajax] => 1
    [page] => 1
    [selected_pagination] => 50
)

GET: Array
(
    [controller] => AdminKatalogy
    [token] => fff8b47487cbad7eeb35fa5fe717cfe0
    [rand] => 1751278280799
    [controllerUri] => AdminKatalogy
)

Received positions via Tools::getValue('katalog'): Array
(
    [0] => tr_2_21_2
    [1] => tr_2_16_3
    [2] => tr_2_17_4
    [3] => tr_2_18_5
    [4] => tr_2_19_6
    [5] => tr_2_14_1
)

Processing: position=0, value=tr_2_21_2
Attempting to update katalog 21 to position 1
✅ Updated katalog 21 to position 1
Processing: position=1, value=tr_2_16_3
Attempting to update katalog 16 to position 2
✅ Updated katalog 16 to position 2
Processing: position=2, value=tr_2_17_4
Attempting to update katalog 17 to position 3
✅ Updated katalog 17 to position 3
Processing: position=3, value=tr_2_18_5
Attempting to update katalog 18 to position 4
✅ Updated katalog 18 to position 4
Processing: position=4, value=tr_2_19_6
Attempting to update katalog 19 to position 5
✅ Updated katalog 19 to position 5
Processing: position=5, value=tr_2_14_1
Attempting to update katalog 14 to position 6
✅ Updated katalog 14 to position 6
=== Katalog::fixDuplicatePositions() START ===
Catalogs read by fixDuplicatePositions: Array
(
    [0] => Array
        (
            [id_katalog] => 21
            [position] => 1
        )

    [1] => Array
        (
            [id_katalog] => 16
            [position] => 2
        )

    [2] => Array
        (
            [id_katalog] => 17
            [position] => 3
        )

    [3] => Array
        (
            [id_katalog] => 18
            [position] => 4
        )

    [4] => Array
        (
            [id_katalog] => 19
            [position] => 5
        )

    [5] => Array
        (
            [id_katalog] => 14
            [position] => 6
        )

)

=== Katalog::fixDuplicatePositions() END ===
=== SUCCESS: 6 positions updated ===
=== PRESTASHOP POSITION UPDATE ===
POST: Array
(
    [katalog] => Array
        (
            [0] => tr_2_14_6
            [1] => tr_2_21_1
            [2] => tr_2_16_2
            [3] => tr_2_17_3
            [4] => tr_2_18_4
            [5] => tr_2_19_5
        )

    [action] => updatePositions
    [id] => 14
    [way] => 0
    [ajax] => 1
    [page] => 1
    [selected_pagination] => 50
)

GET: Array
(
    [controller] => AdminKatalogy
    [token] => fff8b47487cbad7eeb35fa5fe717cfe0
    [rand] => 1751278285660
    [controllerUri] => AdminKatalogy
)

Received positions via Tools::getValue('katalog'): Array
(
    [0] => tr_2_14_6
    [1] => tr_2_21_1
    [2] => tr_2_16_2
    [3] => tr_2_17_3
    [4] => tr_2_18_4
    [5] => tr_2_19_5
)

Processing: position=0, value=tr_2_14_6
Attempting to update katalog 14 to position 1
✅ Updated katalog 14 to position 1
Processing: position=1, value=tr_2_21_1
Attempting to update katalog 21 to position 2
✅ Updated katalog 21 to position 2
Processing: position=2, value=tr_2_16_2
Attempting to update katalog 16 to position 3
✅ Updated katalog 16 to position 3
Processing: position=3, value=tr_2_17_3
Attempting to update katalog 17 to position 4
✅ Updated katalog 17 to position 4
Processing: position=4, value=tr_2_18_4
Attempting to update katalog 18 to position 5
✅ Updated katalog 18 to position 5
Processing: position=5, value=tr_2_19_5
Attempting to update katalog 19 to position 6
✅ Updated katalog 19 to position 6
=== Katalog::fixDuplicatePositions() START ===
Catalogs read by fixDuplicatePositions: Array
(
    [0] => Array
        (
            [id_katalog] => 14
            [position] => 1
        )

    [1] => Array
        (
            [id_katalog] => 21
            [position] => 2
        )

    [2] => Array
        (
            [id_katalog] => 16
            [position] => 3
        )

    [3] => Array
        (
            [id_katalog] => 17
            [position] => 4
        )

    [4] => Array
        (
            [id_katalog] => 18
            [position] => 5
        )

    [5] => Array
        (
            [id_katalog] => 19
            [position] => 6
        )

)

=== Katalog::fixDuplicatePositions() END ===
=== SUCCESS: 6 positions updated ===
