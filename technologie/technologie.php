<?php
/**
 * Modul Technologie potisku pro PrestaShop 8.2.0
 * 
 * <AUTHOR> tý<PERSON>
 * @version 1.2.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

// Autoloader pro namespace PrestaShop\Module\Technologie
spl_autoload_register(function ($class) {
    $prefix = 'PrestaShop\\Module\\Technologie\\';
    $base_dir = __DIR__ . '/src/';

    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }

    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';

    if (file_exists($file)) {
        require $file;
    }
});

/**
 * ObjectModel třída pro PrestaShop admin kompatibilitu
 */
class TechnologieModel extends ObjectModel
{
    /** @var int */
    public $id_technologie;

    /** @var string */
    public $name;

    /** @var string */
    public $description;

    /** @var string */
    public $slug;

    /** @var string */
    public $detailed_description;

    /** @var string */
    public $advantages;

    /** @var string */
    public $applications;

    /** @var string */
    public $image;

    /** @var string */
    public $gallery_images;

    /** @var int */
    public $position;

    /** @var bool */
    public $active;

    /** @var string */
    public $date_add;

    /** @var string */
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'technologie',
        'primary' => 'id_technologie',
        'fields' => [
            'name' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'required' => true,
                'size' => 255
            ],
            'description' => [
                'type' => self::TYPE_HTML,
                'validate' => 'isCleanHtml',
                'required' => false
            ],
            'slug' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isLinkRewrite',
                'required' => true,
                'size' => 255
            ],
            'detailed_description' => [
                'type' => self::TYPE_HTML,
                'validate' => 'isCleanHtml',
                'required' => false
            ],
            'advantages' => [
                'type' => self::TYPE_HTML,
                'validate' => 'isCleanHtml',
                'required' => false
            ],
            'applications' => [
                'type' => self::TYPE_HTML,
                'validate' => 'isCleanHtml',
                'required' => false
            ],
            'image' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isGenericName',
                'required' => false,
                'size' => 255
            ],
            'gallery_images' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isJson',
                'required' => false
            ],
            'position' => [
                'type' => self::TYPE_INT,
                'validate' => 'isUnsignedInt',
                'required' => false
            ],
            'active' => [
                'type' => self::TYPE_BOOL,
                'validate' => 'isBool',
                'required' => false
            ],
            'date_add' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'copy_post' => false
            ],
            'date_upd' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'copy_post' => false
            ],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }
}

/**
 * Hlavní třída modulu
 */
class Technologie extends Module
{
    public function __construct()
    {
        $this->name = 'technologie';
        $this->tab = 'administration';
        $this->version = '1.3.0';
        $this->author = 'Miroslav Urbánek';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '8.0.0',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Technologie potisku');
        $this->description = $this->l('Správa a zobrazení technologií potisku na vlastní podstránce');
        $this->confirmUninstall = $this->l('Opravdu chcete odinstalovat modul Technologie potisku?');
    }

    /**
     * Instalace modulu
     */
    public function install()
    {
        return parent::install() &&
            $this->installDb() &&
            $this->installTab() &&
            $this->registerHook('displayHeader') &&
            $this->registerHook('moduleRoutes');
    }

    /**
     * Odinstalace modulu
     */
    public function uninstall()
    {
        return parent::uninstall() &&
            $this->uninstallDb() &&
            $this->uninstallTab();
    }

    /**
     * Upgrade modulu na novou verzi
     */
    public function upgrade($version)
    {
        // Upgrade z verze 1.2.x na 1.3.x - přidání nových sloupců
        if (version_compare($version, '1.3.0', '<')) {
            return $this->upgradeToV130();
        }

        return true;
    }

    /**
     * Upgrade na verzi 1.3.0 - přidání detailních informací
     */
    private function upgradeToV130()
    {
        $sql = file_get_contents(__DIR__ . '/sql/upgrade.sql');

        if (!$sql) {
            return false;
        }

        // Nahrazení PREFIX_ skutečným prefixem databáze
        $sql = str_replace('PREFIX_', _DB_PREFIX_, $sql);

        // Rozdělení na jednotlivé příkazy
        $statements = array_filter(array_map('trim', explode(';', $sql)));

        foreach ($statements as $statement) {
            if (!empty($statement)) {
                if (!Db::getInstance()->execute($statement)) {
                    PrestaShopLogger::addLog(
                        'Technologie upgrade error: ' . Db::getInstance()->getMsgError(),
                        3,
                        null,
                        'Technologie'
                    );
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Vytvoření databázové tabulky
     */
    private function installDb()
    {
        $sql = file_get_contents(__DIR__ . '/sql/install.sql');

        // Nahrazení PREFIX_ skutečným prefixem databáze
        $sql = str_replace('PREFIX_', _DB_PREFIX_, $sql);

        // Rozdělení na jednotlivé příkazy (kvůli INSERT statements)
        $statements = array_filter(array_map('trim', explode(';', $sql)));

        foreach ($statements as $statement) {
            if (!empty($statement)) {
                if (!Db::getInstance()->execute($statement)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Smazání databázové tabulky
     */
    private function uninstallDb()
    {
        $sql = file_get_contents(__DIR__ . '/sql/uninstall.sql');
        $sql = str_replace('PREFIX_', _DB_PREFIX_, $sql);

        return Db::getInstance()->execute($sql);
    }

    /**
     * Vytvoření admin tabu
     */
    private function installTab()
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminTechnologie';
        $tab->name = [];
        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = 'Správa potisků';
        }
        $tab->id_parent = (int)Tab::getIdFromClassName('IMPROVE');
        $tab->module = $this->name;
        
        return $tab->add();
    }

    /**
     * Smazání admin tabu
     */
    private function uninstallTab()
    {
        $id_tab = (int)Tab::getIdFromClassName('AdminTechnologie');
        if ($id_tab) {
            $tab = new Tab($id_tab);
            return $tab->delete();
        }
        return true;
    }

    /**
     * Hook pro přidání CSS do hlavičky
     */
    public function hookDisplayHeader()
    {
        if ($this->context->controller instanceof TechnologieTechnologieModuleFrontController) {
            $this->context->controller->addCSS($this->_path . 'views/css/front.css');
        }
    }

    /**
     * Hook pro registraci vlastních routes
     */
    public function hookModuleRoutes()
    {
        return [
            'module-technologie-list' => [
                'controller' => 'technologie',
                'rule' => 'reklamni-potisk',
                'keywords' => [],
                'params' => [
                    'fc' => 'module',
                    'module' => 'technologie',
                    'controller' => 'technologie'
                ]
            ],
            'module-technologie-detail' => [
                'controller' => 'technologie',
                'rule' => 'reklamni-potisk/{slug}',
                'keywords' => [
                    'slug' => ['regexp' => '[a-zA-Z0-9\-]+', 'param' => 'slug']
                ],
                'params' => [
                    'fc' => 'module',
                    'module' => 'technologie',
                    'controller' => 'technologie',
                    'action' => 'detail'
                ]
            ]
        ];
    }

    /**
     * Konfigurace modulu
     */
    public function getContent()
    {
        $output = '';

        // Zpracování formuláře pro vymazání cache
        if (Tools::isSubmit('clearRouteCache')) {
            if ($this->clearRouteCache()) {
                $output .= $this->displayConfirmation($this->l('Cache routes byla úspěšně vymazána!'));
            } else {
                $output .= $this->displayError($this->l('Chyba při mazání cache routes!'));
            }
        }

        // Zpracování formuláře pro přeregistraci routes
        if (Tools::isSubmit('reregisterRoutes')) {
            if ($this->reregisterRoutes()) {
                $output .= $this->displayConfirmation($this->l('Routes byly úspěšně přeregistrovány!'));
            } else {
                $output .= $this->displayError($this->l('Chyba při přeregistraci routes!'));
            }
        }

        $output .= $this->displayConfirmation($this->l('Modul je úspěšně nainstalován!'));
        $output .= '<p>' . $this->l('Pro správu technologií přejděte do menu "Správa potisků".') . '</p>';

        // Přidání diagnostických informací a nástrojů
        $output .= $this->renderConfigForm();

        return $output;
    }

    /**
     * Vymazání cache routes
     */
    private function clearRouteCache()
    {
        try {
            // Vymazání cache souborů
            $cacheFiles = [
                _PS_CACHE_DIR_ . 'prod/url_generator_routes.php',
                _PS_CACHE_DIR_ . 'prod/url_matcher_routes.php',
                _PS_CACHE_DIR_ . 'dev/url_generator_routes.php',
                _PS_CACHE_DIR_ . 'dev/url_matcher_routes.php'
            ];

            foreach ($cacheFiles as $file) {
                if (file_exists($file)) {
                    unlink($file);
                }
            }

            // Vymazání cache adresářů
            $cacheDirs = [
                _PS_CACHE_DIR_ . 'prod/',
                _PS_CACHE_DIR_ . 'dev/'
            ];

            foreach ($cacheDirs as $dir) {
                if (is_dir($dir)) {
                    $files = glob($dir . '*routes*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                        }
                    }
                }
            }

            return true;
        } catch (Exception $e) {
            PrestaShopLogger::addLog(
                'Technologie: Error clearing route cache: ' . $e->getMessage(),
                3,
                null,
                'Technologie'
            );
            return false;
        }
    }

    /**
     * Přeregistrace routes
     */
    private function reregisterRoutes()
    {
        try {
            // Odregistrování a znovu registrování hook
            $this->unregisterHook('moduleRoutes');
            $result = $this->registerHook('moduleRoutes');

            // Vymazání cache
            $this->clearRouteCache();

            return $result;
        } catch (Exception $e) {
            PrestaShopLogger::addLog(
                'Technologie: Error reregistering routes: ' . $e->getMessage(),
                3,
                null,
                'Technologie'
            );
            return false;
        }
    }

    /**
     * Renderování konfiguračního formuláře
     */
    private function renderConfigForm()
    {
        $currentRoutes = $this->hookModuleRoutes();

        $html = '<div class="panel">';
        $html .= '<div class="panel-heading">';
        $html .= '<i class="icon-cogs"></i> ' . $this->l('Diagnostika routes');
        $html .= '</div>';
        $html .= '<div class="panel-body">';

        // Aktuální stav routes
        $html .= '<h4>' . $this->l('Aktuální routes:') . '</h4>';
        $html .= '<pre>' . print_r($currentRoutes, true) . '</pre>';

        // Test URL
        $testUrls = [
            'Seznam' => $this->context->link->getModuleLink('technologie', 'technologie'),
            'Detail (sitotisk)' => $this->context->link->getModuleLink('technologie', 'technologie', ['slug' => 'sitotisk'])
        ];

        $html .= '<h4>' . $this->l('Test URL:') . '</h4>';
        $html .= '<ul>';
        foreach ($testUrls as $label => $url) {
            $html .= '<li><strong>' . $label . ':</strong> <a href="' . $url . '" target="_blank">' . $url . '</a></li>';
        }
        $html .= '</ul>';

        // Nástroje
        $html .= '<h4>' . $this->l('Nástroje:') . '</h4>';
        $html .= '<form method="post">';
        $html .= '<button type="submit" name="clearRouteCache" class="btn btn-warning">';
        $html .= '<i class="icon-trash"></i> ' . $this->l('Vymazat cache routes');
        $html .= '</button> ';
        $html .= '<button type="submit" name="reregisterRoutes" class="btn btn-primary">';
        $html .= '<i class="icon-refresh"></i> ' . $this->l('Přeregistrovat routes');
        $html .= '</button>';
        $html .= '</form>';

        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }
}
