<?php
/**
 * Script pro správu vlastních tabů v administraci PrestaShop
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

// Kontrola přístupu
if (!Context::getContext()->employee || !Context::getContext()->employee->isSuperAdmin()) {
    die('Přístup odepřen. Pouze pro super administrátory.');
}

$action = isset($_GET['action']) ? $_GET['action'] : 'view';

echo "<h1>Správa vlastních tabů v administraci</h1>";

switch ($action) {
    case 'create_custom_modules':
        createCustomModulesTab();
        break;
    case 'remove_custom_modules':
        removeCustomModulesTab();
        break;
    case 'reinstall_katalogy':
        reinstallKatalogyTab();
        break;
    default:
        showTabStructure();
        break;
}

function showTabStructure()
{
    echo "<h2>Aktuální struktura tabů</h2>";
    
    // Najdi hlavní parent taby
    $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'tab` WHERE `id_parent` = 0 AND `active` = 1 ORDER BY `position`';
    $main_tabs = Db::getInstance()->executeS($sql);
    
    foreach ($main_tabs as $main_tab) {
        $tab_obj = new Tab($main_tab['id_tab']);
        $tab_name = $tab_obj->name[Context::getContext()->language->id] ?? $main_tab['class_name'];
        
        echo "<h3>📁 " . strtoupper($tab_name) . " (" . $main_tab['class_name'] . ")</h3>";
        
        // Najdi child taby
        showChildTabs($main_tab['id_tab'], 1);
    }
    
    echo "<h2>Akce</h2>";
    echo "<p><a href='?action=create_custom_modules' style='background: #007cba; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🔧 Vytvořit sekci 'Vlastní moduly'</a></p>";
    echo "<p><a href='?action=remove_custom_modules' style='background: #dc3545; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🗑️ Odstranit sekci 'Vlastní moduly'</a></p>";
    echo "<p><a href='?action=reinstall_katalogy' style='background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🔄 Přeinstalovat tab Katalogy</a></p>";
}

function showChildTabs($parent_id, $level = 0)
{
    $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
    
    $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'tab` WHERE `id_parent` = ' . (int)$parent_id . ' AND `active` = 1 ORDER BY `position`';
    $child_tabs = Db::getInstance()->executeS($sql);
    
    foreach ($child_tabs as $child_tab) {
        $tab_obj = new Tab($child_tab['id_tab']);
        $tab_name = $tab_obj->name[Context::getContext()->language->id] ?? $child_tab['class_name'];
        
        $icon = $child_tab['icon'] ? $child_tab['icon'] : 'folder';
        $module_info = $child_tab['module'] ? " <em>(modul: {$child_tab['module']})</em>" : "";
        
        echo "<p>{$indent}📄 {$tab_name} ({$child_tab['class_name']}){$module_info}</p>";
        
        // Rekurzivně zobraz další úrovně
        showChildTabs($child_tab['id_tab'], $level + 1);
    }
}

function createCustomModulesTab()
{
    echo "<h2>Vytváření sekce 'Vlastní moduly'</h2>";

    // Načti CustomModulesTabManager
    require_once(dirname(__FILE__) . '/classes/CustomModulesTabManager.php');

    // Zkontroluj, zda už existuje
    if (CustomModulesTabManager::customModulesSectionExists()) {
        $existing_id = Tab::getIdFromClassName('AdminCustomModules');
        echo "<p style='color: orange;'>⚠️ Sekce 'Vlastní moduly' už existuje (ID: $existing_id)</p>";
        echo "<p><a href='?'>← Zpět na přehled</a></p>";
        return;
    }

    // Vytvoř sekci pomocí CustomModulesTabManager
    $result = CustomModulesTabManager::createOrGetCustomModulesTab();

    if ($result) {
        echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' byla úspěšně vytvořena (ID: $result)</p>";
        echo "<p style='color: blue;'>📍 Umístěna hned po sekci 'KONFIGURACE'</p>";
    } else {
        echo "<p style='color: red;'>❌ Chyba při vytváření sekce 'Vlastní moduly'</p>";
    }

    echo "<p><a href='?'>← Zpět na přehled</a></p>";
}

function removeCustomModulesTab()
{
    echo "<h2>Odstraňování sekce 'Vlastní moduly'</h2>";

    // Načti CustomModulesTabManager
    require_once(dirname(__FILE__) . '/classes/CustomModulesTabManager.php');

    if (!CustomModulesTabManager::customModulesSectionExists()) {
        echo "<p style='color: orange;'>⚠️ Sekce 'Vlastní moduly' neexistuje</p>";
        echo "<p><a href='?'>← Zpět na přehled</a></p>";
        return;
    }

    // Zkontroluj child taby
    $custom_modules = CustomModulesTabManager::getCustomModulesTabs();
    if (!empty($custom_modules)) {
        $child_count = count($custom_modules);
        echo "<p style='color: red;'>❌ Nelze odstranit sekci 'Vlastní moduly' - obsahuje $child_count aktivních podsekcí:</p>";
        echo "<ul>";
        foreach ($custom_modules as $module_tab) {
            echo "<li>{$module_tab['name']} ({$module_tab['class_name']})</li>";
        }
        echo "</ul>";
        echo "<p>Nejdříve odinstalujte všechny moduly v této sekci.</p>";
        echo "<p><a href='?'>← Zpět na přehled</a></p>";
        return;
    }

    // Použij CustomModulesTabManager pro odstranění
    CustomModulesTabManager::cleanupCustomModulesTabIfEmpty();

    if (!CustomModulesTabManager::customModulesSectionExists()) {
        echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' byla úspěšně odstraněna</p>";
    } else {
        echo "<p style='color: red;'>❌ Chyba při odstraňování sekce 'Vlastní moduly'</p>";
    }

    echo "<p><a href='?'>← Zpět na přehled</a></p>";
}

function reinstallKatalogyTab()
{
    echo "<h2>Přeinstalace tabu Katalogy</h2>";
    
    // Nejdříve odstraň existující tab
    $existing_id = Tab::getIdFromClassName('AdminKatalogy');
    if ($existing_id) {
        $tab = new Tab($existing_id);
        $tab->delete();
        echo "<p>🗑️ Starý tab Katalogy odstraněn</p>";
    }
    
    // Získej instanci modulu
    $module = Module::getInstanceByName('katalogy');
    if (!$module) {
        echo "<p style='color: red;'>❌ Modul katalogy není nainstalován</p>";
        echo "<p><a href='?'>← Zpět na přehled</a></p>";
        return;
    }
    
    // Zavolej createTab metodu
    if (method_exists($module, 'createTab')) {
        // Použij reflection pro přístup k private metodě
        $reflection = new ReflectionClass($module);
        $method = $reflection->getMethod('createTab');
        $method->setAccessible(true);
        
        if ($method->invoke($module)) {
            echo "<p style='color: green;'>✅ Tab Katalogy byl úspěšně přeinstalován</p>";
        } else {
            echo "<p style='color: red;'>❌ Chyba při vytváření tabu Katalogy</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Metoda createTab neexistuje v modulu</p>";
    }
    
    echo "<p><a href='?'>← Zpět na přehled</a></p>";
}

echo "<hr>";
echo "<p><small>Script: " . __FILE__ . "</small></p>";
?>
