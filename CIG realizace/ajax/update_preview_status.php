<?php
/**
 * AJAX endpoint for updating order preview status
 * CIG Realizace - Phase 04
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and history logger
require_once '../includes/auth_check.php';
require_once '../classes/HistoryLogger.php';
require_once '../includes/email_functions.php';

// Check if user is logged in and has permission
$current_user = getCurrentUser();
if (!$current_user || !in_array($current_user['role'], ['admin', 'grafik'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Nemáte oprávnění k této akci']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

// Get and validate input
$order_id = intval($_POST['order_id'] ?? 0);
$new_status = $_POST['preview_status'] ?? '';
$notes = trim($_POST['notes'] ?? '');

if (!$order_id) {
    echo json_encode(['success' => false, 'message' => 'Neplatné ID objednávky']);
    exit;
}

// Validate status
$allowed_statuses = ['not_created', 'sent_to_client', 'approved'];
if (!in_array($new_status, $allowed_statuses)) {
    echo json_encode(['success' => false, 'message' => 'Neplatný stav náhledu']);
    exit;
}

try {
    $pdo = getDbConnection();
    $pdo->beginTransaction();
    
    // Get current order data
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Objednávka nebyla nalezena');
    }
    
    $old_status = $order['preview_status'];
    
    // Don't update if status is the same
    if ($old_status === $new_status) {
        echo json_encode(['success' => true, 'message' => 'Stav nebyl změněn']);
        exit;
    }
    
    // Prepare update data
    $update_data = [
        'preview_status' => $new_status,
        'preview_status_date' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    // If status is approved, set additional fields
    if ($new_status === 'approved') {
        $update_data['preview_approved_date'] = date('Y-m-d');
        // Calculate expected delivery date (14 days from approval)
        $update_data['expected_delivery_date'] = date('Y-m-d', strtotime('+14 days'));
        // Update order status to in_progress if it's still pending
        if ($order['status'] === 'pending') {
            $update_data['status'] = 'in_progress';
        }
    }
    
    // Update order
    $set_clauses = [];
    $params = [];
    foreach ($update_data as $field => $value) {
        $set_clauses[] = "$field = ?";
        $params[] = $value;
    }
    $params[] = $order_id;
    
    $sql = "UPDATE orders SET " . implode(', ', $set_clauses) . " WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    // Log the change using HistoryLogger
    HistoryLogger::logPreviewStatusChange($order_id, $current_user['id'], $old_status, $new_status, $notes);

    $pdo->commit();

    // Send email notification for status change
    try {
        sendPreviewStatusChangeEmail($order_id, $old_status, $new_status);
    } catch (Exception $e) {
        // Log email error but don't fail the main operation
        error_log("Error sending preview status change email: " . $e->getMessage());
    }
    
    // Prepare response message
    $status_labels = [
        'not_created' => 'Nevytvořen',
        'sent_to_client' => 'Odeslán klientovi',
        'approved' => 'Schválen'
    ];
    
    $message = "Stav náhledu byl úspěšně změněn na '{$status_labels[$new_status]}'";
    
    if ($new_status === 'approved') {
        $message .= ". Objednávka byla přesunuta do kalendáře s očekávaným termínem dodání " . 
                   date('d.m.Y', strtotime($update_data['expected_delivery_date']));
    }
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => [
            'order_id' => $order_id,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'preview_approved_date' => $update_data['preview_approved_date'] ?? null,
            'expected_delivery_date' => $update_data['expected_delivery_date'] ?? null
        ]
    ]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error updating preview status: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při aktualizaci stavu: ' . $e->getMessage()
    ]);
}
?>
