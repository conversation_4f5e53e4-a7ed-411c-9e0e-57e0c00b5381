<?php
/**
 * AJAX endpoint for updating user notification preferences
 * CIG Realizace - Phase 08
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and email functions
require_once '../includes/auth_check.php';
require_once '../includes/email_functions.php';

// Check if user is logged in
$current_user = getCurrentUser();
if (!$current_user) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášeni']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

try {
    // Get action
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_preferences':
            // Validate and sanitize input
            $preferences = [
                'order_completion' => isset($_POST['order_completion']) ? 1 : 0,
                'preview_status_change' => isset($_POST['preview_status_change']) ? 1 : 0,
                'overdue_alerts' => isset($_POST['overdue_alerts']) ? 1 : 0,
                'daily_summary' => isset($_POST['daily_summary']) ? 1 : 0,
                'weekly_summary' => isset($_POST['weekly_summary']) ? 1 : 0,
                'email_format' => in_array($_POST['email_format'] ?? '', ['html', 'text']) ? $_POST['email_format'] : 'html',
                'frequency' => in_array($_POST['frequency'] ?? '', ['immediate', 'daily', 'weekly']) ? $_POST['frequency'] : 'immediate'
            ];
            
            // Update preferences
            $success = updateUserNotificationPreferences($current_user['id'], $preferences);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Nastavení notifikací bylo úspěšně uloženo',
                    'preferences' => $preferences
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se uložit nastavení notifikací'
                ]);
            }
            break;
            
        case 'get_preferences':
            // Get current preferences
            $preferences = getUserNotificationPreferences($current_user['id']);
            
            if ($preferences) {
                echo json_encode([
                    'success' => true,
                    'preferences' => $preferences
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se načíst nastavení notifikací'
                ]);
            }
            break;
            
        case 'test_notification':
            // Send test notification to user
            $notificationType = $_POST['notification_type'] ?? 'order_completed';
            
            $emailManager = new EmailManager();
            $testData = getTestNotificationData($notificationType, $current_user);
            
            $success = $emailManager->sendEmailNow(
                $current_user['email'],
                $current_user['full_name'],
                $testData['subject'],
                $testData['template'],
                $testData['data']
            );
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Test notifikace byla odeslána na Vaši emailovou adresu'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se odeslat test notifikaci'
                ]);
            }
            break;
            
        case 'unsubscribe_all':
            // Unsubscribe from all notifications
            $preferences = [
                'order_completion' => 0,
                'preview_status_change' => 0,
                'overdue_alerts' => 0,
                'daily_summary' => 0,
                'weekly_summary' => 0,
                'email_format' => 'html',
                'frequency' => 'immediate'
            ];
            
            $success = updateUserNotificationPreferences($current_user['id'], $preferences);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Byly jste odhlášeni ze všech emailových notifikací',
                    'preferences' => $preferences
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se odhlásit z notifikací'
                ]);
            }
            break;
            
        case 'subscribe_all':
            // Subscribe to all notifications
            $preferences = [
                'order_completion' => 1,
                'preview_status_change' => 1,
                'overdue_alerts' => 1,
                'daily_summary' => 0, // Keep daily summary off by default
                'weekly_summary' => 1,
                'email_format' => 'html',
                'frequency' => 'immediate'
            ];
            
            $success = updateUserNotificationPreferences($current_user['id'], $preferences);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Byly jste přihlášeni k emailovým notifikacím',
                    'preferences' => $preferences
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Nepodařilo se přihlásit k notifikacím'
                ]);
            }
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => 'Neplatná akce'
            ]);
            break;
    }
    
} catch (Exception $e) {
    error_log("Notification preferences AJAX error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Došlo k chybě při zpracování požadavku'
    ]);
}

/**
 * Get test notification data based on type
 */
function getTestNotificationData($type, $user) {
    $baseData = [
        'recipient_name' => $user['full_name'],
        'order_url' => 'http://localhost:8000/orders/detail.php?id=1',
        'dashboard_url' => 'http://localhost:8000/dashboard.php'
    ];
    
    switch ($type) {
        case 'order_completed':
            return [
                'subject' => 'Test: Objednávka dokončena - TEST-001',
                'template' => 'order_completed',
                'data' => array_merge($baseData, [
                    'order_code' => 'TEST-001',
                    'sales_rep_name' => $user['full_name'],
                    'order_date' => date('d.m.Y'),
                    'completion_date' => date('d.m.Y H:i'),
                    'items_count' => 5,
                    'technologies' => 'Test technologie'
                ])
            ];
            
        case 'preview_status_changed':
            return [
                'subject' => 'Test: Změna stavu náhledu - TEST-001',
                'template' => 'preview_status_changed',
                'data' => array_merge($baseData, [
                    'order_code' => 'TEST-001',
                    'old_status_text' => 'Probíhá',
                    'new_status_text' => 'Schváleno',
                    'changed_by' => $user['full_name'],
                    'change_date' => date('d.m.Y H:i'),
                    'approved' => true,
                    'delivery_date' => date('d.m.Y', strtotime('+7 days'))
                ])
            ];
            
        case 'daily_summary':
            return [
                'subject' => 'Test: Denní souhrn - ' . date('d.m.Y'),
                'template' => 'daily_summary',
                'data' => array_merge($baseData, [
                    'user_name' => $user['full_name'],
                    'date' => date('d.m.Y'),
                    'new_orders' => 3,
                    'completed_orders' => 2,
                    'pending_previews' => 1,
                    'overdue_orders' => 0,
                    'total_activity' => 6
                ])
            ];
            
        case 'overdue_alert':
            return [
                'subject' => 'Test: Upozornění na zpožděné objednávky',
                'template' => 'overdue_alert',
                'data' => array_merge($baseData, [
                    'admin_name' => $user['full_name'],
                    'overdue_count' => 2,
                    'alert_date' => date('d.m.Y'),
                    'orders' => [
                        [
                            'order_code' => 'TEST-001',
                            'sales_rep' => 'Test obchodník',
                            'expected_delivery_date' => date('Y-m-d', strtotime('-3 days')),
                            'preview_status' => 'approved',
                            'technologies' => 'Test technologie'
                        ],
                        [
                            'order_code' => 'TEST-002',
                            'sales_rep' => 'Test obchodník 2',
                            'expected_delivery_date' => date('Y-m-d', strtotime('-1 day')),
                            'preview_status' => 'approved',
                            'technologies' => 'Jiná technologie'
                        ]
                    ]
                ])
            ];
            
        default:
            return [
                'subject' => 'Test notifikace z CIG Realizace',
                'template' => 'order_completed',
                'data' => array_merge($baseData, [
                    'order_code' => 'TEST-001',
                    'sales_rep_name' => $user['full_name'],
                    'order_date' => date('d.m.Y'),
                    'completion_date' => date('d.m.Y H:i'),
                    'items_count' => 1,
                    'technologies' => 'Test'
                ])
            ];
    }
}
?>
