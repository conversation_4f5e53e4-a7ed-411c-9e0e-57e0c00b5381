<?php
/**
 * <PERSON><PERSON><PERSON> "Cena na dotaz" pro PrestaShop
 * Verze 7.1.0 - Přidána podpora pro dynamické změny variant produktu
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class PriceInquiry extends Module
{
    public function __construct()
    {
        $this->name = 'priceinquiry';
        $this->tab = 'front_office_features';
        $this->version = '8.0.0';
        $this->author = '<PERSON><PERSON><PERSON>';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = ['min' => '1.7', 'max' => _PS_VERSION_];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Cena na dotaz');
        $this->description = $this->l('Zobrazuje "Cena na dotaz" místo ceny 0 Kč a umožňuje zákazníkům poslat dotaz na cenu.');

        error_log("PriceInquiry: Module constructed, version: " . $this->version);
    }

    public function install()
    {
        include(dirname(__FILE__).'/sql/install.php');

        return parent::install() &&
            $this->registerHook('displayProductPriceBlock') && // Pro přehled produktů
            $this->registerHook('displayProductActions') &&    // Pro detail produktu
            $this->registerHook('displayProductAdditionalInfo') && // Záložní hook
            $this->registerHook('displayReassurance') &&       // Další záložní hook
            $this->registerHook('displayHeader') &&
            $this->registerHook('displayFooter') &&
            Configuration::updateValue('PRICE_INQUIRY_ENABLED', 1) &&
            Configuration::updateValue('PRICE_INQUIRY_EMAIL', Configuration::get('PS_SHOP_EMAIL')) &&
            Configuration::updateValue('PRICE_INQUIRY_BUTTON_TEXT', $this->l('Zjistit cenu')) &&
            Configuration::updateValue('PRICE_INQUIRY_PRICE_TEXT', $this->l('Cena na dotaz')) &&
            Configuration::updateValue('PRICE_INQUIRY_SEND_ADMIN_EMAIL', 1) &&
            Configuration::updateValue('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL', 1);
    }

    public function uninstall()
    {
        include(dirname(__FILE__).'/sql/uninstall.php');

        return parent::uninstall() &&
            Configuration::deleteByName('PRICE_INQUIRY_ENABLED') &&
            Configuration::deleteByName('PRICE_INQUIRY_EMAIL') &&
            Configuration::deleteByName('PRICE_INQUIRY_BUTTON_TEXT') &&
            Configuration::deleteByName('PRICE_INQUIRY_PRICE_TEXT') &&
            Configuration::deleteByName('PRICE_INQUIRY_SEND_ADMIN_EMAIL') &&
            Configuration::deleteByName('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL');
    }

    public function hookDisplayHeader()
    {
        error_log("PriceInquiry: hookDisplayHeader called on page: " . $this->context->controller->php_self);

        // Zkontrolujeme, jestli soubory existují
        $cssPath = $this->_path.'views/css/front.css';
        $jsPath = $this->_path.'views/js/front.js';

        error_log("PriceInquiry: CSS path: " . $cssPath);
        error_log("PriceInquiry: JS path: " . $jsPath);

        if (file_exists(_PS_MODULE_DIR_ . 'priceinquiry/views/css/front.css')) {
            $this->context->controller->addCSS($this->_path.'views/css/front.css');
            error_log("PriceInquiry: CSS added successfully");
        } else {
            error_log("PriceInquiry: CSS file not found!");
        }

        if (file_exists(_PS_MODULE_DIR_ . 'priceinquiry/views/js/front.js')) {
            $this->context->controller->addJS($this->_path.'views/js/front.js');
            error_log("PriceInquiry: JS added successfully");
        } else {
            error_log("PriceInquiry: JS file not found!");
        }
    }

    /**
     * Hook pro PŘEHLED PRODUKTŮ (kategorie).
     */
    public function hookDisplayProductPriceBlock($params)
    {
        if ($this->context->controller->php_self === 'product' || !Configuration::get('PRICE_INQUIRY_ENABLED')) {
            return '';
        }
        if ($params['type'] !== 'before_price' && $params['type'] !== 'unit_price') {
            return '';
        }
        $product_id = (int)($params['product']['id_product'] ?? $params['product']['id'] ?? 0);
        if (!$product_id) {
            return '';
        }
        $price = Product::getPriceStatic($product_id, true, null, 2, null, false, true);
        if ($price > 0) {
            return '';
        }
        $price_text = Configuration::get('PRICE_INQUIRY_PRICE_TEXT') ?: $this->l('Cena na dotaz');
        return '<div class="pi-category-text" data-id-product="'.$product_id.'">
                    <style>
                        .product-miniature[data-id-product="'.$product_id.'"] .product-price-and-shipping { display: none !important; }
                    </style>
                    '.$price_text.'
                </div>';
    }

    /**
     * Hook pro DETAIL PRODUKTU.
     * Verze 7.1.0 - Kontroluje cenu při načtení, JavaScript pak reaguje na změny variant
     */
    public function hookDisplayProductActions($params)
    {
        error_log("PriceInquiry: hookDisplayProductActions called");

        if ($this->context->controller->php_self !== 'product' || !Configuration::get('PRICE_INQUIRY_ENABLED')) {
            error_log("PriceInquiry: Hook skipped - not product page or not enabled");
            return '';
        }

        $product = $this->context->controller->getProduct();
        if (!Validate::isLoadedObject($product)) {
            return '';
        }

        $id_product_attribute = (int)Tools::getValue('id_product_attribute', Product::getDefaultAttribute($product->id));
        $price = Product::getPriceStatic($product->id, true, $id_product_attribute);

        // Debug informace
        error_log("PriceInquiry Debug: Product ID: {$product->id}, Attribute ID: {$id_product_attribute}, Price: {$price}");

        // Zobrazujeme blok pouze při nulové ceně při načtení
        // JavaScript pak bude reagovat na změny variant
        $showBlock = ($price <= 0);

        $image = Image::getCover($product->id);
        $productImage = $this->context->link->getImageLink($product->link_rewrite, $image['id_image'] ?? null, 'home_default');

        $this->context->smarty->assign([
            'product_id' => $product->id,
            'product_name' => $product->name,
            'product_reference' => $product->reference,
            'product_image' => $productImage,
            'is_logged' => $this->context->customer->isLogged(),
            'customer_name' => $this->context->customer->isLogged() ? $this->context->customer->firstname.' '.$this->context->customer->lastname : '',
            'customer_email' => $this->context->customer->isLogged() ? $this->context->customer->email : '',
            'show_block' => $showBlock,
            'current_price' => $price
        ]);

        return $this->display(__FILE__, 'views/templates/front/inquiry_block_detail.tpl');
    }

    /**
     * Hook displayProductButtons - zakážeme, aby se nezobrazovalo vícekrát
     */
    public function hookDisplayProductButtons($params)
    {
        error_log("PriceInquiry: hookDisplayProductButtons called - returning empty");
        return '';
    }

    /**
     * Záložní hook pro případ, že displayProductActions nefunguje
     */
    public function hookDisplayProductAdditionalInfo($params)
    {
        error_log("PriceInquiry: hookDisplayProductAdditionalInfo called");
        // Vrátíme prázdný string, aby se nezobrazovalo vícekrát
        return '';
    }

    /**
     * Další záložní hook
     */
    public function hookDisplayReassurance($params)
    {
        error_log("PriceInquiry: hookDisplayReassurance called");
        // Vrátíme prázdný string, aby se nezobrazovalo vícekrát
        return '';
    }

    public function hookDisplayFooter($params)
    {
        if (!Configuration::get('PRICE_INQUIRY_ENABLED')) return '';
        $this->context->smarty->assign(['link' => $this->context->link]);
        return $this->display(__FILE__, 'views/templates/front/inquiry_modal.tpl');
    }
    
    public function getContent()
    {
        $output = '';

        // Zpracování akcí
        if (Tools::isSubmit('submitPriceInquiryConfig')) {
            $output .= $this->processConfiguration();
        } elseif (Tools::isSubmit('markResolved')) {
            $output .= $this->markInquiryResolved(Tools::getValue('id_inquiry'));
        } elseif (Tools::isSubmit('deleteInquiry')) {
            $output .= $this->deleteInquiry(Tools::getValue('id_inquiry'));
        }

        // Zobrazení obsahu podle akce
        $action = Tools::getValue('action', 'list');

        switch ($action) {
            case 'config':
                $output .= $this->displayConfigurationForm();
                break;
            case 'view':
                $output .= $this->displayInquiryDetail(Tools::getValue('id_inquiry'));
                break;
            default:
                $output .= $this->displayInquiriesList();
                break;
        }

        return $output;
    }

    private function processConfiguration()
    {
        $enabled = (bool)Tools::getValue('PRICE_INQUIRY_ENABLED');
        $email = Tools::getValue('PRICE_INQUIRY_EMAIL');
        $buttonText = Tools::getValue('PRICE_INQUIRY_BUTTON_TEXT');
        $priceText = Tools::getValue('PRICE_INQUIRY_PRICE_TEXT');
        $sendAdminEmail = (bool)Tools::getValue('PRICE_INQUIRY_SEND_ADMIN_EMAIL');
        $sendCustomerEmail = (bool)Tools::getValue('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL');

        Configuration::updateValue('PRICE_INQUIRY_ENABLED', $enabled);
        Configuration::updateValue('PRICE_INQUIRY_EMAIL', $email);
        Configuration::updateValue('PRICE_INQUIRY_BUTTON_TEXT', $buttonText);
        Configuration::updateValue('PRICE_INQUIRY_PRICE_TEXT', $priceText);
        Configuration::updateValue('PRICE_INQUIRY_SEND_ADMIN_EMAIL', $sendAdminEmail);
        Configuration::updateValue('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL', $sendCustomerEmail);

        return $this->displayConfirmation($this->l('Nastavení bylo uloženo.'));
    }

    protected function displayConfigurationForm()
    {
        // Získáme aktuální hodnoty
        $enabled = Configuration::get('PRICE_INQUIRY_ENABLED');
        $email = Configuration::get('PRICE_INQUIRY_EMAIL');
        $buttonText = Configuration::get('PRICE_INQUIRY_BUTTON_TEXT');
        $priceText = Configuration::get('PRICE_INQUIRY_PRICE_TEXT');
        $sendAdminEmail = Configuration::get('PRICE_INQUIRY_SEND_ADMIN_EMAIL');
        $sendCustomerEmail = Configuration::get('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL');

        $fields_form = array(
            'form' => array(
                'legend' => array(
                    'title' => $this->l('Konfigurace modulu "Cena na dotaz"'),
                    'icon' => 'icon-cogs'
                ),
                'input' => array(
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Povolit modul'),
                        'name' => 'PRICE_INQUIRY_ENABLED',
                        'desc' => $this->l('Zapne/vypne zobrazování "Cena na dotaz" u produktů s nulovou cenou'),
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Zapnuto')
                            ),
                            array(
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Vypnuto')
                            )
                        ),
                    ),
                    array(
                        'type' => 'text',
                        'label' => $this->l('E-mailová adresa'),
                        'name' => 'PRICE_INQUIRY_EMAIL',
                        'desc' => $this->l('E-mail, na který budou zasílány dotazy na cenu'),
                        'size' => 50,
                        'required' => false
                    ),
                    array(
                        'type' => 'text',
                        'label' => $this->l('Text tlačítka'),
                        'name' => 'PRICE_INQUIRY_BUTTON_TEXT',
                        'desc' => $this->l('Text zobrazený na tlačítku pro dotaz na cenu'),
                        'size' => 30,
                        'required' => true
                    ),
                    array(
                        'type' => 'text',
                        'label' => $this->l('Text místo ceny'),
                        'name' => 'PRICE_INQUIRY_PRICE_TEXT',
                        'desc' => $this->l('Text zobrazený místo ceny 0 Kč'),
                        'size' => 30,
                        'required' => true
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Poslat e-mail administrátorovi'),
                        'name' => 'PRICE_INQUIRY_SEND_ADMIN_EMAIL',
                        'desc' => $this->l('Poslat notifikační e-mail administrátorovi při novém dotazu'),
                        'values' => array(
                            array(
                                'id' => 'admin_email_on',
                                'value' => 1,
                                'label' => $this->l('Ano')
                            ),
                            array(
                                'id' => 'admin_email_off',
                                'value' => 0,
                                'label' => $this->l('Ne')
                            )
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Poslat potvrzení zákazníkovi'),
                        'name' => 'PRICE_INQUIRY_SEND_CUSTOMER_EMAIL',
                        'desc' => $this->l('Poslat potvrzovací e-mail zákazníkovi'),
                        'values' => array(
                            array(
                                'id' => 'customer_email_on',
                                'value' => 1,
                                'label' => $this->l('Ano')
                            ),
                            array(
                                'id' => 'customer_email_off',
                                'value' => 0,
                                'label' => $this->l('Ne')
                            )
                        ),
                    )
                ),
                'submit' => array(
                    'title' => $this->l('Uložit'),
                    'class' => 'btn btn-default pull-right'
                )
            )
        );

        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $lang = new Language((int)Configuration::get('PS_LANG_DEFAULT'));
        $helper->default_form_language = $lang->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') ? Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG') : 0;
        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submitPriceInquiryConfig';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false).'&configure='.$this->name.'&tab_module='.$this->tab.'&module_name='.$this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->tpl_vars = array(
            'fields_value' => array(
                'PRICE_INQUIRY_ENABLED' => $enabled,
                'PRICE_INQUIRY_EMAIL' => $email,
                'PRICE_INQUIRY_BUTTON_TEXT' => $buttonText,
                'PRICE_INQUIRY_PRICE_TEXT' => $priceText,
                'PRICE_INQUIRY_SEND_ADMIN_EMAIL' => $sendAdminEmail,
                'PRICE_INQUIRY_SEND_CUSTOMER_EMAIL' => $sendCustomerEmail,
            ),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id
        );

        return $helper->generateForm(array($fields_form));
    }

    private function displayInquiriesList()
    {
        $output = '';

        // Navigace
        $output .= '<div class="panel">';
        $output .= '<div class="panel-heading">';
        $output .= '<i class="icon-list"></i> ' . $this->l('Přehled dotazů na cenu');
        $output .= '<span class="panel-heading-action">';
        $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&action=config" class="btn btn-default">';
        $output .= '<i class="icon-cog"></i> ' . $this->l('Konfigurace');
        $output .= '</a>';
        $output .= '</span>';
        $output .= '</div>';

        // Získání dotazů z databáze
        $sql = 'SELECT * FROM `'._DB_PREFIX_.'price_inquiry` ORDER BY `date_add` DESC';
        $inquiries = Db::getInstance()->executeS($sql);

        if (empty($inquiries)) {
            $output .= '<div class="alert alert-info">' . $this->l('Zatím nebyly odeslány žádné dotazy.') . '</div>';
        } else {
            $output .= '<table class="table">';
            $output .= '<thead>';
            $output .= '<tr>';
            $output .= '<th>' . $this->l('Datum') . '</th>';
            $output .= '<th>' . $this->l('Zákazník') . '</th>';
            $output .= '<th>' . $this->l('Produkt') . '</th>';
            $output .= '<th>' . $this->l('Stav') . '</th>';
            $output .= '<th>' . $this->l('Akce') . '</th>';
            $output .= '</tr>';
            $output .= '</thead>';
            $output .= '<tbody>';

            foreach ($inquiries as $inquiry) {
                $output .= '<tr>';
                $output .= '<td>' . date('d.m.Y H:i', strtotime($inquiry['date_add'])) . '</td>';
                $output .= '<td>' . htmlspecialchars($inquiry['customer_name']) . '<br><small>' . htmlspecialchars($inquiry['customer_email']) . '</small></td>';
                $output .= '<td>' . htmlspecialchars($inquiry['product_name']) . '</td>';
                $output .= '<td>';
                if ($inquiry['resolved']) {
                    $output .= '<span class="badge badge-success">' . $this->l('Vyřešeno') . '</span>';
                } else {
                    $output .= '<span class="badge badge-warning">' . $this->l('Nevyřešeno') . '</span>';
                }
                $output .= '</td>';
                $output .= '<td>';
                $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&action=view&id_inquiry='.$inquiry['id_price_inquiry'].'" class="btn btn-default btn-xs">';
                $output .= '<i class="icon-eye"></i> ' . $this->l('Detail');
                $output .= '</a> ';
                if (!$inquiry['resolved']) {
                    $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&markResolved=1&id_inquiry='.$inquiry['id_price_inquiry'].'" class="btn btn-success btn-xs">';
                    $output .= '<i class="icon-check"></i> ' . $this->l('Označit jako vyřešeno');
                    $output .= '</a> ';
                }
                $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&deleteInquiry=1&id_inquiry='.$inquiry['id_price_inquiry'].'" class="btn btn-danger btn-xs" onclick="return confirm(\''.$this->l('Opravdu chcete smazat tento dotaz?').'\')">';
                $output .= '<i class="icon-trash"></i> ' . $this->l('Smazat');
                $output .= '</a>';
                $output .= '</td>';
                $output .= '</tr>';
            }

            $output .= '</tbody>';
            $output .= '</table>';
        }

        $output .= '</div>';

        return $output;
    }

    private function markInquiryResolved($id_inquiry)
    {
        $sql = 'UPDATE `'._DB_PREFIX_.'price_inquiry` SET `resolved` = 1 WHERE `id_price_inquiry` = '.(int)$id_inquiry;
        if (Db::getInstance()->execute($sql)) {
            return $this->displayConfirmation($this->l('Dotaz byl označen jako vyřešený.'));
        } else {
            return $this->displayError($this->l('Chyba při označování dotazu.'));
        }
    }

    private function deleteInquiry($id_inquiry)
    {
        $sql = 'DELETE FROM `'._DB_PREFIX_.'price_inquiry` WHERE `id_price_inquiry` = '.(int)$id_inquiry;
        if (Db::getInstance()->execute($sql)) {
            return $this->displayConfirmation($this->l('Dotaz byl smazán.'));
        } else {
            return $this->displayError($this->l('Chyba při mazání dotazu.'));
        }
    }

    private function displayInquiryDetail($id_inquiry)
    {
        $output = '';

        // Navigace zpět
        $output .= '<div class="panel">';
        $output .= '<div class="panel-heading">';
        $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'" class="btn btn-default">';
        $output .= '<i class="icon-arrow-left"></i> ' . $this->l('Zpět na přehled');
        $output .= '</a>';
        $output .= '</div>';

        // Získání dotazu z databáze
        $sql = 'SELECT * FROM `'._DB_PREFIX_.'price_inquiry` WHERE `id_price_inquiry` = '.(int)$id_inquiry;
        $inquiry = Db::getInstance()->getRow($sql);

        if (!$inquiry) {
            $output .= '<div class="alert alert-danger">' . $this->l('Dotaz nebyl nalezen.') . '</div>';
        } else {
            $output .= '<h3>' . $this->l('Detail dotazu #') . $inquiry['id_price_inquiry'] . '</h3>';

            $output .= '<div class="row">';
            $output .= '<div class="col-md-6">';
            $output .= '<div class="panel">';
            $output .= '<div class="panel-heading">' . $this->l('Informace o zákazníkovi') . '</div>';
            $output .= '<div class="panel-body">';
            $output .= '<p><strong>' . $this->l('Jméno:') . '</strong> ' . htmlspecialchars($inquiry['customer_name']) . '</p>';
            $output .= '<p><strong>' . $this->l('E-mail:') . '</strong> <a href="mailto:' . htmlspecialchars($inquiry['customer_email']) . '">' . htmlspecialchars($inquiry['customer_email']) . '</a></p>';
            $output .= '<p><strong>' . $this->l('Telefon:') . '</strong> ' . htmlspecialchars($inquiry['customer_phone']) . '</p>';
            if (!empty($inquiry['customer_company'])) {
                $output .= '<p><strong>' . $this->l('Firma:') . '</strong> ' . htmlspecialchars($inquiry['customer_company']) . '</p>';
            }
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';

            $output .= '<div class="col-md-6">';
            $output .= '<div class="panel">';
            $output .= '<div class="panel-heading">' . $this->l('Informace o produktu') . '</div>';
            $output .= '<div class="panel-body">';
            $output .= '<p><strong>' . $this->l('Název:') . '</strong> ' . htmlspecialchars($inquiry['product_name']) . '</p>';
            $output .= '<p><strong>' . $this->l('Kód produktu:') . '</strong> ' . htmlspecialchars($inquiry['product_reference']) . '</p>';
            $output .= '<p><strong>' . $this->l('Požadované množství:') . '</strong> ' . (int)$inquiry['quantity'] . '</p>';
            $output .= '<p><strong>' . $this->l('Datum dotazu:') . '</strong> ' . date('d.m.Y H:i', strtotime($inquiry['date_add'])) . '</p>';
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';

            if (!empty($inquiry['message'])) {
                $output .= '<div class="panel">';
                $output .= '<div class="panel-heading">' . $this->l('Zpráva od zákazníka') . '</div>';
                $output .= '<div class="panel-body">';
                $output .= '<p>' . nl2br(htmlspecialchars($inquiry['message'])) . '</p>';
                $output .= '</div>';
                $output .= '</div>';
            }

            // Akce
            $output .= '<div class="panel">';
            $output .= '<div class="panel-heading">' . $this->l('Akce') . '</div>';
            $output .= '<div class="panel-body">';

            if (!$inquiry['resolved']) {
                $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&markResolved=1&id_inquiry='.$inquiry['id_price_inquiry'].'" class="btn btn-success">';
                $output .= '<i class="icon-check"></i> ' . $this->l('Označit jako vyřešeno');
                $output .= '</a> ';
            } else {
                $output .= '<span class="badge badge-success">' . $this->l('Vyřešeno') . '</span> ';
            }

            $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&deleteInquiry=1&id_inquiry='.$inquiry['id_price_inquiry'].'" class="btn btn-danger" onclick="return confirm(\''.$this->l('Opravdu chcete smazat tento dotaz?').'\')">';
            $output .= '<i class="icon-trash"></i> ' . $this->l('Smazat dotaz');
            $output .= '</a>';

            $output .= '</div>';
            $output .= '</div>';
        }

        $output .= '</div>';

        return $output;
    }
}
