/**
 * Speciální diagnostika pro sledování přepínání variant
 * Spusť na stránce produktu před přepnutím varianty
 */

console.log('🔍 === DIAGNOSTIKA PŘEPÍNÁNÍ VARIANT ===');

// Globální proměnné pro sledování
window.variantDebug = {
    logs: [],
    initialState: {},
    mutations: [],
    priceElements: [],
    
    log: function(message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = { timestamp, message, data };
        this.logs.push(logEntry);
        console.log(`[${timestamp}] ${message}`, data || '');
    },
    
    captureState: function(label) {
        const state = {
            label: label,
            timestamp: new Date().toLocaleTimeString(),
            
            // Naše bloky
            piBlocks: Array.from(document.querySelectorAll('.price-inquiry-block-detail')).map(block => ({
                visible: window.getComputedStyle(block).display !== 'none',
                style: block.style.display,
                showBlock: block.getAttribute('data-show-block'),
                initialPrice: block.getAttribute('data-initial-price'),
                innerHTML: block.innerHTML.substring(0, 100) + '...'
            })),
            
            // Cenové elementy
            priceElements: Array.from(document.querySelectorAll('.product-price, .product-prices')).map(el => ({
                selector: el.className ? '.' + el.className.split(' ').join('.') : el.tagName,
                text: el.textContent.trim(),
                visible: window.getComputedStyle(el).display !== 'none',
                style: el.style.display,
                computedDisplay: window.getComputedStyle(el).display
            })),
            
            // CSS styly
            ourStyles: Array.from(document.querySelectorAll('#pi-dynamic-styles, style[data-pi-generated]')).map(style => ({
                id: style.id,
                content: style.textContent
            })),
            
            // Variant select
            variantSelect: (() => {
                const select = document.querySelector('select[name="group[1]"]');
                return select ? {
                    value: select.value,
                    selectedText: select.options[select.selectedIndex]?.text
                } : null;
            })(),
            
            // Add to cart elementy
            cartElements: Array.from(document.querySelectorAll('.product-add-to-cart .product-quantity, .product-add-to-cart .add')).map(el => ({
                className: el.className,
                visible: window.getComputedStyle(el).display !== 'none',
                style: el.style.display
            }))
        };
        
        this.log(`📸 Stav zachycen: ${label}`, state);
        return state;
    },
    
    startMonitoring: function() {
        this.log('🚀 Spouštím monitoring...');
        
        // Zachytíme počáteční stav
        this.initialState = this.captureState('POČÁTEČNÍ STAV');
        
        // Sledování změn DOM
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || 
                    (mutation.type === 'attributes' && 
                     ['style', 'class', 'data-show-block'].includes(mutation.attributeName))) {
                    
                    this.log(`🔄 DOM změna: ${mutation.type}`, {
                        target: mutation.target.tagName + (mutation.target.className ? '.' + mutation.target.className : ''),
                        attributeName: mutation.attributeName,
                        oldValue: mutation.oldValue,
                        newValue: mutation.target.getAttribute ? mutation.target.getAttribute(mutation.attributeName) : null
                    });
                    
                    // Zachytíme stav po změně
                    setTimeout(() => {
                        this.captureState(`PO DOM ZMĚNĚ (${mutation.type})`);
                    }, 10);
                }
            });
        });
        
        this.observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeOldValue: true,
            attributeFilter: ['style', 'class', 'data-show-block', 'data-initial-price']
        });
        
        // Sledování variant selectu
        const variantSelect = document.querySelector('select[name="group[1]"]');
        if (variantSelect) {
            this.log('✅ Variant select nalezen, přidávám listener');
            
            variantSelect.addEventListener('change', (event) => {
                this.log(`🔄 VARIANT SELECT ZMĚNA: ${event.target.value}`, {
                    oldValue: this.initialState.variantSelect?.value,
                    newValue: event.target.value,
                    selectedText: event.target.options[event.target.selectedIndex]?.text
                });
                
                // Zachytíme stav ihned po změně
                this.captureState('IHNED PO ZMĚNĚ SELECTU');
                
                // A pak v intervalech
                setTimeout(() => this.captureState('100ms PO ZMĚNĚ'), 100);
                setTimeout(() => this.captureState('500ms PO ZMĚNĚ'), 500);
                setTimeout(() => this.captureState('1000ms PO ZMĚNĚ'), 1000);
                setTimeout(() => this.captureState('2000ms PO ZMĚNĚ'), 2000);
            });
        } else {
            this.log('❌ Variant select nenalezen!');
        }
        
        // Sledování našich funkcí
        if (typeof PriceInquiryJS !== 'undefined') {
            this.log('✅ PriceInquiryJS nalezen, přidávám intercepty');
            
            // Intercept našich funkcí
            const originalShow = PriceInquiryJS.showPriceInquiry;
            const originalHide = PriceInquiryJS.hidePriceInquiry;
            const originalCheck = PriceInquiryJS.checkAndUpdatePriceInquiry;
            const originalApplyCss = PriceInquiryJS.applyCssHiding;
            
            PriceInquiryJS.showPriceInquiry = () => {
                this.log('🟢 PriceInquiryJS.showPriceInquiry() VOLÁNA');
                this.captureState('PŘED showPriceInquiry');
                originalShow.call(PriceInquiryJS);
                setTimeout(() => this.captureState('PO showPriceInquiry'), 10);
            };
            
            PriceInquiryJS.hidePriceInquiry = () => {
                this.log('🔴 PriceInquiryJS.hidePriceInquiry() VOLÁNA');
                this.captureState('PŘED hidePriceInquiry');
                originalHide.call(PriceInquiryJS);
                setTimeout(() => this.captureState('PO hidePriceInquiry'), 10);
            };
            
            PriceInquiryJS.checkAndUpdatePriceInquiry = () => {
                this.log('🔍 PriceInquiryJS.checkAndUpdatePriceInquiry() VOLÁNA');
                this.captureState('PŘED checkAndUpdatePriceInquiry');
                originalCheck.call(PriceInquiryJS);
                setTimeout(() => this.captureState('PO checkAndUpdatePriceInquiry'), 10);
            };
            
            PriceInquiryJS.applyCssHiding = (hide) => {
                this.log(`🎨 PriceInquiryJS.applyCssHiding(${hide}) VOLÁNA`);
                this.captureState(`PŘED applyCssHiding(${hide})`);
                originalApplyCss.call(PriceInquiryJS, hide);
                setTimeout(() => this.captureState(`PO applyCssHiding(${hide})`), 10);
            };
        } else {
            this.log('❌ PriceInquiryJS nenalezen!');
        }
        
        this.log('✅ Monitoring spuštěn! Nyní přepni variantu...');
    },
    
    stopMonitoring: function() {
        if (this.observer) {
            this.observer.disconnect();
            this.log('🛑 Monitoring zastaven');
        }
    },
    
    getReport: function() {
        console.log('📊 === FINÁLNÍ REPORT ===');
        console.log('Celkem logů:', this.logs.length);
        
        this.logs.forEach((log, index) => {
            console.log(`${index + 1}. [${log.timestamp}] ${log.message}`, log.data || '');
        });
        
        return {
            logs: this.logs,
            initialState: this.initialState,
            totalLogs: this.logs.length
        };
    }
};

// Spustíme monitoring
variantDebug.startMonitoring();

console.log('🎯 MONITORING SPUŠTĚN!');
console.log('📝 Nyní přepni variantu a sleduj výstup');
console.log('📊 Pro finální report zavolej: variantDebug.getReport()');
console.log('🛑 Pro zastavení: variantDebug.stopMonitoring()');
