/**
 * Order Detail Styles
 * CIG Realizace - Phase 05
 */

/* Order Header */
.order-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
}

.order-header .breadcrumb {
    background: transparent;
    margin-bottom: 10px;
}

.order-header .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.order-header .breadcrumb-item.active {
    color: white;
}

/* Status Management */
.status-form {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.status-form .form-label {
    font-weight: 600;
    color: #495057;
}

.status-form .form-select,
.status-form .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.status-form .form-select:focus,
.status-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.status-form .btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
}

/* Order Items Table */
.items-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.items-table .table {
    margin-bottom: 0;
}

.items-table .table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px 12px;
}

.items-table .table tbody tr {
    transition: background-color 0.3s ease;
}

.items-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

.items-table .table tbody td {
    padding: 12px;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* Technology Assignment */
.technology-select {
    min-width: 150px;
    border-radius: 6px;
    border: 1px solid #ced4da;
    font-size: 0.9rem;
}

.technology-input {
    min-width: 150px;
    border-radius: 6px;
    border: 1px solid #ced4da;
    font-size: 0.9rem;
}

.technology-save-btn {
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* Item Actions */
.item-actions {
    display: flex;
    gap: 5px;
    align-items: center;
}

.item-action-btn {
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.item-action-btn:hover {
    transform: translateY(-1px);
}

.item-action-btn.btn-edit {
    background: #ffc107;
    color: #212529;
}

.item-action-btn.btn-delete {
    background: #dc3545;
    color: white;
}

.item-action-btn.btn-save {
    background: #28a745;
    color: white;
}

.item-action-btn.btn-cancel {
    background: #6c757d;
    color: white;
}

/* Relevance Toggle */
.relevance-toggle {
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.relevance-toggle:hover {
    transform: scale(1.1);
}

.relevance-toggle.relevant {
    color: #28a745;
}

.relevance-toggle.irrelevant {
    color: #dc3545;
}

/* Order History */
.history-timeline {
    position: relative;
    padding-left: 30px;
}

.history-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.history-item {
    position: relative;
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.history-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #e9ecef;
}

.history-item .history-meta {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.history-item .history-description {
    font-weight: 500;
    color: #495057;
}

/* Status Badges */
.status-badge {
    font-size: 0.85rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-not-created {
    background: #ffc107;
    color: #212529;
}

.status-sent-to-client {
    background: #17a2b8;
    color: white;
}

.status-approved {
    background: #28a745;
    color: white;
}

/* Inventory Status Badges */
.inventory-not-in-stock {
    background: #dc3545;
    color: white;
}

.inventory-ordered {
    background: #ffc107;
    color: #212529;
}

.inventory-in-stock {
    background: #28a745;
    color: white;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    color: #667eea;
    font-size: 1.5rem;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.notification.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
    .order-header {
        padding: 20px 0;
    }
    
    .status-form {
        padding: 15px;
    }
    
    .items-table .table {
        font-size: 0.9rem;
    }
    
    .technology-select,
    .technology-input {
        min-width: 120px;
    }
    
    .item-actions {
        flex-direction: column;
        gap: 3px;
    }
    
    .history-timeline {
        padding-left: 20px;
    }
    
    .history-item::before {
        left: -17px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
