Komunikovat budeme v č<PERSON><PERSON><PERSON>ě, ste<PERSON><PERSON> tak psát komentáře v češtině, k=od budeš psát v angličtině.

Pro ideální funkčnost změn a tvorby projektu vytvoř script, pomoc<PERSON> kter<PERSON> budeš moct sám nahrávat provedené změny na localu přímo na FTP, což budeš dělat v<PERSON>dy, <PERSON><PERSON><PERSON> do<PERSON> nějakou část projektu, opravu, úpravu, změnu. Přístupové údaje přikládám.

Hosting a databáze u společnosti webglobe.

FTP:
Název serveru **************
Port222
Jméno:
Heslo:

Databáze:
Server/Host: db.dw189.webglobe.com
Mysql8
Externí přístup k databázovému serveru (port 3306) je povolený.
PHP 8.3

Veď si dokumentaci projektu v jednom souboru, k<PERSON><PERSON> budeš aktualizovat a nebudeš stále dokola vytvářet nový.

