# Contact Module pro PrestaShop

## Popis
Modul pro vytvoření kontaktní str<PERSON>ky s informacemi o firmě, mapou a obchodními zástupci.

## Funkce
- Zobrazení kontaktních informací firmy
- Integrace Google Maps
- Správa obchodní<PERSON> zástupců
- Responzivní design
- Administrační rozhraní pro konfiguraci

## Instalace

### Způsob 1: Přes administraci PrestaShop
1. Přihlaste se do administrace PrestaShop
2. Jděte na **Moduly** → **Správce modulů**
3. Klikněte na **Nahrát modul**
4. <PERSON><PERSON><PERSON><PERSON> soubor `contactmodule.zip`
5. Klikněte na **Nahrát tento modul**
6. Po nahrání klikněte na **Konfigurovat**

### Způsob 2: Manuální instalace
1. Rozbalte obsah archivu do složky `/modules/contactmodule/` ve vašem PrestaShop
2. Jděte do administrace → **Moduly** → **Správce modulů**
3. Najděte "Contact Page Module" a klikněte na **Instalovat**

## Konfigurace
Po instalaci můžete modul konfigurovat v administraci:

1. **Moduly** → **Správce modulů**
2. Najděte "Contact Page Module"
3. Klikněte na **Konfigurovat**
4. Vyplňte:
   - Název společnosti
   - Adresu
   - Telefon
   - Email
   - Embed kód Google Maps
   - Bankovní informace

## Použití

### Způsob 1: Shortcode (doporučeno)
Po instalaci modulu můžete jednoduše vložit shortcode do obsahu CMS stránky:
```
[contactmodule]
```

### Způsob 2: Automatická aktualizace CMS stránky
1. V konfiguraci modulu klikněte na **"Aktualizovat CMS stránku"**
2. Obsah CMS stránky s ID 22 se automaticky nahradí obsahem z `cms-minimal.html`
3. Data se automaticky aktualizují podle konfigurace modulu

### Způsob 3: Vložení shortcode tlačítkem
1. V konfiguraci modulu klikněte na **"Vložit shortcode"**
2. Shortcode `[contactmodule]` se automaticky přidá na konec CMS stránky

## Nové funkce v této verzi

✅ **Automatická synchronizace dat** - změny v administraci se okamžitě projeví na frontend
✅ **Shortcode podpora** - použijte `[contactmodule]` kdekoli v CMS obsahu
✅ **Dynamická správa obchodních zástupců** - přidávání/odebírání přímo v administraci
✅ **Inline styly pro CMS** - perfektní zobrazení i v CMS stránkách
✅ **AJAX načítání** - rychlé a spolehlivé načítání obsahu
✅ **Automatické tlačítka** - jednoduché vložení do CMS stránky

## Požadavky
- PrestaShop 1.7.x nebo 8.x
- PHP 7.4 nebo vyšší

## Autor
Miroslav Urbánek

## Verze
1.0.0

## Licence
Tento modul je poskytován "jak je" bez jakýchkoli záruk.
