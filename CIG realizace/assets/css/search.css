/**
 * Advanced Search and Filter Styles
 * CIG Realizace - Phase 09
 */

/* Global Search Bar */
.global-search {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 4px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.global-search input {
    flex: 1;
    border: none;
    background: white;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.global-search input:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.global-search .btn {
    background: transparent;
    border: none;
    color: white;
    padding: 8px 12px;
    margin-left: 4px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.global-search .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    display: none;
}

.search-suggestions.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.suggestion-group {
    border-bottom: 1px solid #f0f0f0;
}

.suggestion-group:last-child {
    border-bottom: none;
}

.suggestion-header {
    padding: 8px 16px;
    background: #f8f9fa;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: #666;
    letter-spacing: 0.5px;
}

.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.suggestion-item:hover {
    background: #f8f9fa;
    border-left-color: #667eea;
}

.suggestion-item .suggestion-text {
    font-weight: 500;
    color: #333;
}

.suggestion-item .suggestion-subtitle {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.suggestion-item .suggestion-type {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: 8px;
}

.suggestion-type.order {
    background: #e3f2fd;
    color: #1976d2;
}

.suggestion-type.catalog {
    background: #f3e5f5;
    color: #7b1fa2;
}

.suggestion-type.technology {
    background: #e8f5e8;
    color: #388e3c;
}

/* Advanced Filter Panel */
.advanced-filters {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-header h5 {
    margin: 0;
    font-weight: 600;
}

.filter-actions {
    display: flex;
    gap: 8px;
}

.filter-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    color: white;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.filter-actions .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.filter-content {
    padding: 20px;
}

.filter-groups {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.filter-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;
}

.filter-group label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

.filter-group .form-control,
.filter-group .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.filter-group .form-control:focus,
.filter-group .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Multi-select styling */
.multi-select {
    min-height: 100px;
}

.multi-select option:checked {
    background: #667eea;
    color: white;
}

/* Checkbox groups */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.checkbox-group label:hover {
    color: #667eea;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

/* Date range inputs */
.date-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.date-range input {
    font-size: 14px;
}

/* Technology filter with suggestions */
.technology-filter-container {
    position: relative;
}

.technology-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 150px;
    overflow-y: auto;
    z-index: 100;
    display: none;
}

.technology-suggestions.show {
    display: block;
}

.technology-suggestion {
    padding: 8px 12px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.technology-suggestion:hover {
    background: #f8f9fa;
}

/* Saved Filters */
.saved-filters {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.saved-filters-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 12px 16px;
    font-weight: 600;
}

.saved-filters-content {
    padding: 16px;
}

.filter-list {
    margin-bottom: 16px;
}

.filter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.filter-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.filter-name {
    font-weight: 500;
    color: #495057;
}

.filter-item-actions {
    display: flex;
    gap: 4px;
}

.filter-item-actions .btn {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 4px;
}

/* Quick Presets */
.quick-presets {
    border-top: 1px solid #e9ecef;
    padding-top: 16px;
}

.quick-presets h6 {
    margin-bottom: 12px;
    font-weight: 600;
    color: #495057;
}

.preset-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.preset-btn {
    padding: 6px 12px;
    border: 1px solid #28a745;
    background: transparent;
    color: #28a745;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preset-btn:hover {
    background: #28a745;
    color: white;
    transform: translateY(-1px);
}

.preset-btn.active {
    background: #28a745;
    color: white;
}

/* Export buttons */
.export-buttons {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.export-btn {
    padding: 8px 16px;
    border: 1px solid #6c757d;
    background: transparent;
    color: #6c757d;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.export-btn:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-1px);
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search Results Styles */
.search-results-header {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-results-section {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
}

.result-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.result-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.result-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-card .card-header h6 {
    margin: 0;
}

.result-card .card-header a {
    color: white;
    font-weight: 600;
}

.result-card .card-body {
    padding: 16px;
}

.catalog-results,
.technology-results {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
}

.catalog-item,
.technology-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;
}

.catalog-item:hover,
.technology-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.welcome-screen {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-tips {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    text-align: left;
    display: inline-block;
}

.search-tips ul {
    margin: 0;
}

.search-tips li {
    padding: 4px 0;
}

.empty-results {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
    .filter-groups {
        grid-template-columns: 1fr;
    }

    .search-input-group {
        flex-direction: column;
        gap: 8px;
    }

    .global-search .btn {
        margin-left: 0;
        width: 100%;
    }

    .date-range {
        grid-template-columns: 1fr;
    }

    .preset-buttons {
        flex-direction: column;
    }

    .export-buttons {
        flex-direction: column;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .catalog-results,
    .technology-results {
        grid-template-columns: 1fr;
    }

    .catalog-item,
    .technology-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
