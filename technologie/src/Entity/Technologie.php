<?php
/**
 * Doctrine Entity pro technologie potisku
 */

declare(strict_types=1);

namespace PrestaShop\Module\Technologie\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Table(name="ps_technologie")
 * @ORM\Entity(repositoryClass="PrestaShop\Module\Technologie\Repository\TechnologieRepository")
 * @ORM\HasLifecycleCallbacks
 */
class Technologie
{
    /**
     * @ORM\Id
     * @ORM\Column(name="id_technologie", type="integer")
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private ?int $id = null;

    /**
     * @ORM\Column(name="name", type="string", length=255, nullable=false)
     */
    private string $name;

    /**
     * @ORM\Column(name="description", type="text", nullable=true)
     */
    private ?string $description = null;

    /**
     * @ORM\Column(name="image", type="string", length=255, nullable=true)
     */
    private ?string $image = null;

    /**
     * @ORM\Column(name="position", type="integer", nullable=false, options={"default": 0})
     */
    private int $position = 0;

    /**
     * @ORM\Column(name="active", type="boolean", nullable=false, options={"default": true})
     */
    private bool $active = true;

    /**
     * @ORM\Column(name="date_add", type="datetime", nullable=false)
     */
    private \DateTime $dateAdd;

    /**
     * @ORM\Column(name="date_upd", type="datetime", nullable=false)
     */
    private \DateTime $dateUpd;

    /**
     * @ORM\Column(name="slug", type="string", length=255, nullable=true, unique=true)
     */
    private ?string $slug = null;

    /**
     * @ORM\Column(name="detailed_description", type="text", nullable=true)
     */
    private ?string $detailedDescription = null;

    /**
     * @ORM\Column(name="advantages", type="text", nullable=true)
     */
    private ?string $advantages = null;

    /**
     * @ORM\Column(name="applications", type="text", nullable=true)
     */
    private ?string $applications = null;

    /**
     * @ORM\Column(name="gallery_images", type="text", nullable=true)
     */
    private ?string $galleryImages = null;

    public function __construct()
    {
        $this->dateAdd = new \DateTime();
        $this->dateUpd = new \DateTime();
        // Inicializace výchozích hodnot pro povinné properties
        $this->name = '';
        $this->position = 0;
        $this->active = true;
    }

    // Getters
    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name ?? '';
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function getDateAdd(): \DateTime
    {
        return $this->dateAdd;
    }

    public function getDateUpd(): \DateTime
    {
        return $this->dateUpd;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function getDetailedDescription(): ?string
    {
        return $this->detailedDescription;
    }

    public function getAdvantages(): ?string
    {
        return $this->advantages;
    }

    public function getApplications(): ?string
    {
        return $this->applications;
    }

    public function getGalleryImages(): ?string
    {
        return $this->galleryImages;
    }

    // Setters
    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function setImage(?string $image): self
    {
        $this->image = $image;
        return $this;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;
        return $this;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;
        return $this;
    }

    public function setSlug(?string $slug): self
    {
        $this->slug = $slug;
        return $this;
    }

    public function setDetailedDescription(?string $detailedDescription): self
    {
        $this->detailedDescription = $detailedDescription;
        return $this;
    }

    public function setAdvantages(?string $advantages): self
    {
        $this->advantages = $advantages;
        return $this;
    }

    public function setApplications(?string $applications): self
    {
        $this->applications = $applications;
        return $this;
    }

    public function setGalleryImages(?string $galleryImages): self
    {
        $this->galleryImages = $galleryImages;
        return $this;
    }

    /**
     * @ORM\PreUpdate
     */
    public function updateDateUpd(): void
    {
        $this->dateUpd = new \DateTime();
    }

    /**
     * Získání cesty k obrázku pro zobrazení
     */
    public function getImagePath(): ?string
    {
        if (!$this->image) {
            return null;
        }
        
        return _MODULE_DIR_ . 'technologie/uploads/' . $this->image;
    }

    /**
     * Získání absolutní cesty k obrázku
     */
    public function getImageUrl(): ?string
    {
        if (!$this->image) {
            return null;
        }

        return __PS_BASE_URI__ . 'modules/technologie/uploads/' . $this->image;
    }

    /**
     * Získání pole obrázků galerie z JSON
     */
    public function getGalleryImagesArray(): array
    {
        if (!$this->galleryImages) {
            return [];
        }

        $decoded = json_decode($this->galleryImages, true);
        return is_array($decoded) ? $decoded : [];
    }

    /**
     * Nastavení pole obrázků galerie jako JSON
     */
    public function setGalleryImagesArray(array $images): self
    {
        $this->galleryImages = json_encode($images);
        return $this;
    }

    /**
     * Přidání obrázku do galerie
     */
    public function addGalleryImage(string $imagePath): self
    {
        $images = $this->getGalleryImagesArray();
        if (!in_array($imagePath, $images)) {
            $images[] = $imagePath;
            $this->setGalleryImagesArray($images);
        }
        return $this;
    }

    /**
     * Odebrání obrázku z galerie
     */
    public function removeGalleryImage(string $imagePath): self
    {
        $images = $this->getGalleryImagesArray();
        $key = array_search($imagePath, $images);
        if ($key !== false) {
            unset($images[$key]);
            $this->setGalleryImagesArray(array_values($images));
        }
        return $this;
    }

    /**
     * Automatické generování slug z názvu
     */
    public function generateSlug(): self
    {
        if (!$this->name) {
            return $this;
        }

        // Převod na malá písmena a odstranění diakritiky
        $slug = strtolower($this->name);

        // Odstranění diakritiky
        $slug = iconv('UTF-8', 'ASCII//TRANSLIT', $slug);

        // Nahrazení nealfanumerických znaků pomlčkami
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);

        // Odstranění pomlček na začátku a konci
        $slug = trim($slug, '-');

        $this->slug = $slug;
        return $this;
    }

    /**
     * Získání URL pro detail stránku technologie
     */
    public function getDetailUrl(): string
    {
        if (!$this->slug) {
            $this->generateSlug();
        }

        return __PS_BASE_URI__ . 'modules/technologie/detail/' . $this->slug;
    }

    /**
     * Získání pole výhod z textu (oddělené \n)
     */
    public function getAdvantagesArray(): array
    {
        if (!$this->advantages) {
            return [];
        }

        return array_filter(array_map('trim', explode("\n", $this->advantages)));
    }

    /**
     * Nastavení výhod z pole
     */
    public function setAdvantagesArray(array $advantages): self
    {
        $this->advantages = implode("\n", $advantages);
        return $this;
    }

    /**
     * Získání pole oblastí použití z textu (oddělené \n)
     */
    public function getApplicationsArray(): array
    {
        if (!$this->applications) {
            return [];
        }

        return array_filter(array_map('trim', explode("\n", $this->applications)));
    }

    /**
     * Nastavení oblastí použití z pole
     */
    public function setApplicationsArray(array $applications): self
    {
        $this->applications = implode("\n", $applications);
        return $this;
    }

    /**
     * Získání preview výhod (první 3 položky)
     */
    public function getAdvantagesPreview(): array
    {
        if (!$this->advantages) {
            return [];
        }

        $advantages = array_filter(array_map('trim', explode("\n", $this->advantages)));
        return array_slice($advantages, 0, 3);
    }
}
