<?php
/**
 * Debug script pro zobrazení pozic tabů
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

echo "<h1>Debug pozic tabů v administraci</h1>";

// Získej všechny top-level taby
$sql = 'SELECT t.id_tab, t.class_name, t.position, t.icon, tl.name 
        FROM `' . _DB_PREFIX_ . 'tab` t
        LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
        WHERE t.id_parent = 0 AND t.active = 1 
        ORDER BY t.position';

$tabs = Db::getInstance()->executeS($sql);

echo "<h2>Aktuální pozice top-level tabů:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Pozice</th><th>Název</th><th>Class Name</th><th>Icon</th><th>ID</th></tr>";

foreach ($tabs as $tab) {
    $style = '';
    if ($tab['class_name'] == 'AdminCustomModules') {
        $style = 'background-color: #ffeb3b;'; // Zvýrazni vlastní sekci
    } elseif ($tab['class_name'] == 'AdminParentPreferences') {
        $style = 'background-color: #e3f2fd;'; // Zvýrazni KONFIGURACE
    }
    
    echo "<tr style='$style'>";
    echo "<td>{$tab['position']}</td>";
    echo "<td>{$tab['name']}</td>";
    echo "<td>{$tab['class_name']}</td>";
    echo "<td>{$tab['icon']}</td>";
    echo "<td>{$tab['id_tab']}</td>";
    echo "</tr>";
}

echo "</table>";

// Najdi pozici KONFIGURACE
$config_position = null;
$custom_position = null;

foreach ($tabs as $tab) {
    if ($tab['class_name'] == 'AdminParentPreferences') {
        $config_position = $tab['position'];
    }
    if ($tab['class_name'] == 'AdminCustomModules') {
        $custom_position = $tab['position'];
    }
}

echo "<h2>Analýza pozic:</h2>";
if ($config_position !== null) {
    echo "<p><strong>KONFIGURACE (AdminParentPreferences):</strong> pozice $config_position</p>";
} else {
    echo "<p style='color: red;'>❌ KONFIGURACE (AdminParentPreferences) nenalezena</p>";
}

if ($custom_position !== null) {
    echo "<p><strong>VLASTNÍ MODULY (AdminCustomModules):</strong> pozice $custom_position</p>";
    
    if ($config_position !== null) {
        if ($custom_position == $config_position + 1) {
            echo "<p style='color: green;'>✅ Vlastní moduly jsou správně umístěny hned za KONFIGURACE</p>";
        } else {
            echo "<p style='color: red;'>❌ Vlastní moduly NEJSOU hned za KONFIGURACE (měly by být na pozici " . ($config_position + 1) . ")</p>";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ VLASTNÍ MODULY (AdminCustomModules) neexistují</p>";
}

// Navrhni opravu
if ($custom_position !== null && $config_position !== null && $custom_position != $config_position + 1) {
    echo "<h2>Oprava pozice:</h2>";
    echo "<p><a href='?action=fix_position' style='background: #f44336; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🔧 Opravit pozici Vlastních modulů</a></p>";
}

echo "<p><a href='manage-custom-tabs.php'>← Zpět na správu tabů</a></p>";

// Zpracování akce
if (isset($_GET['action']) && $_GET['action'] == 'fix_position') {
    echo "<hr><h2>Oprava pozice...</h2>";
    
    if ($config_position !== null && $custom_position !== null) {
        $target_position = $config_position + 1;
        
        // Přesuň všechny taby za KONFIGURACE o jednu pozici doprava
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                SET position = position + 1 
                WHERE position >= ' . (int)$target_position . ' AND id_parent = 0 
                AND class_name != "AdminCustomModules"';
        
        if (Db::getInstance()->execute($sql)) {
            echo "<p>✅ Posunuly se taby za pozicí $config_position</p>";
        }
        
        // Nastav správnou pozici pro Vlastní moduly
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'tab` 
                SET position = ' . (int)$target_position . ' 
                WHERE class_name = "AdminCustomModules"';
        
        if (Db::getInstance()->execute($sql)) {
            echo "<p style='color: green;'>✅ Vlastní moduly přesunuty na pozici $target_position</p>";
            echo "<p><a href='?'>🔄 Obnovit zobrazení</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Chyba při přesunu Vlastních modulů</p>";
        }
    }
}

echo "<hr>";
echo "<p><small>Script: " . __FILE__ . "</small></p>";
?>
