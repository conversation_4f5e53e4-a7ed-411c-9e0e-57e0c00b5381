<?php
/**
 * AJAX endpoint for marking order as completed
 * CIG Realizace - Phase 08
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and required classes
require_once '../includes/auth_check.php';
require_once '../classes/HistoryLogger.php';
require_once '../includes/email_functions.php';

// Check if user is logged in and has permission
$current_user = getCurrentUser();
if (!$current_user || !in_array($current_user['role'], ['admin', 'realizator'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Nemáte oprávnění k této akci']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metoda není povolena']);
    exit;
}

// Get and validate input
$order_id = intval($_POST['order_id'] ?? 0);
$notes = trim($_POST['notes'] ?? '');

if (!$order_id) {
    echo json_encode(['success' => false, 'message' => 'Neplatné ID objednávky']);
    exit;
}

try {
    $pdo = getDbConnection();
    $pdo->beginTransaction();
    
    // Get current order data
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Objednávka nebyla nalezena');
    }
    
    // Check if order is already completed
    if ($order['is_completed']) {
        echo json_encode(['success' => true, 'message' => 'Objednávka je již označena jako dokončená']);
        exit;
    }
    
    // Check if order can be completed (must be approved)
    if ($order['preview_status'] !== 'approved') {
        throw new Exception('Objednávku lze dokončit pouze pokud je náhled schválen');
    }
    
    // Update order as completed
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET is_completed = 1,
            status = 'completed',
            completed_date = CURRENT_TIMESTAMP,
            completed_by = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");
    
    $stmt->execute([$current_user['id'], $order_id]);
    
    // Log the completion using HistoryLogger
    HistoryLogger::logOrderCompletion($order_id, $current_user['id'], $notes);
    
    $pdo->commit();
    
    // Send email notification for order completion
    try {
        sendOrderCompletionEmail($order_id);
    } catch (Exception $e) {
        // Log email error but don't fail the main operation
        error_log("Error sending order completion email: " . $e->getMessage());
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Objednávka byla úspěšně označena jako dokončená',
        'data' => [
            'order_id' => $order_id,
            'completed_date' => date('Y-m-d H:i:s'),
            'completed_by' => $current_user['full_name']
        ]
    ]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Error completing order: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při označování objednávky jako dokončené: ' . $e->getMessage()
    ]);
}
?>
