<?php
/**
 * Script pro přidání vlastní sekce do existujícího modulu
 * 
 * POUŽITÍ:
 * 1. Zkopírujte CustomModulesTabManager.php do složky classes/ vašeho modulu
 * 2. Spusťte tento script: modules/vas_modul/add-to-existing-module.php?module=vas_modul&controller=AdminVasModul
 * 3. Script upraví váš modul a přesune tab do vlastní sekce
 */

// Základní kontrola prostředí
if (!defined('_PS_VERSION_')) {
    require_once(dirname(__FILE__) . '/../../config/config.inc.php');
}

$module_name = Tools::getValue('module');
$controller_class = Tools::getValue('controller');

echo "<h1>Přidání vlastní sekce do existujícího modulu</h1>";

if (!$module_name || !$controller_class) {
    echo "<h2>Použití:</h2>";
    echo "<p>Přidejte parametry do URL:</p>";
    echo "<p><code>?module=nazev_modulu&controller=AdminNazevModulu</code></p>";
    echo "<p><strong>Příklad:</strong> <code>?module=katalogy&controller=AdminKatalogy</code></p>";
    exit;
}

echo "<h2>Zpracování modulu: <strong>$module_name</strong></h2>";
echo "<p>Controller: <strong>$controller_class</strong></p>";

// Zkontroluj, zda modul existuje
$module = Module::getInstanceByName($module_name);
if (!$module) {
    echo "<p style='color: red;'>❌ Modul '$module_name' neexistuje nebo není nainstalován</p>";
    exit;
}

echo "<p style='color: green;'>✅ Modul nalezen</p>";

// Zkontroluj, zda CustomModulesTabManager existuje
$manager_path = _PS_MODULE_DIR_ . $module_name . '/classes/CustomModulesTabManager.php';
if (!file_exists($manager_path)) {
    echo "<p style='color: red;'>❌ CustomModulesTabManager.php nenalezen v $manager_path</p>";
    echo "<p><strong>Akce:</strong> Zkopírujte CustomModulesTabManager.php do složky classes/ vašeho modulu</p>";
    exit;
}

echo "<p style='color: green;'>✅ CustomModulesTabManager.php nalezen</p>";

// Načti CustomModulesTabManager
require_once($manager_path);

// Zkontroluj aktuální stav tabu
$current_tab_id = Tab::getIdFromClassName($controller_class);
if (!$current_tab_id) {
    echo "<p style='color: red;'>❌ Tab '$controller_class' neexistuje</p>";
    exit;
}

echo "<p style='color: green;'>✅ Tab '$controller_class' nalezen (ID: $current_tab_id)</p>";

// Získej informace o aktuálním tabu
$sql = 'SELECT t.*, tl.name 
        FROM `' . _DB_PREFIX_ . 'tab` t
        LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
        WHERE t.id_tab = ' . (int)$current_tab_id;

$tab_info = Db::getInstance()->getRow($sql);
if ($tab_info) {
    echo "<p><strong>Aktuální umístění:</strong> Parent ID: {$tab_info['id_parent']}, Pozice: {$tab_info['position']}</p>";
}

// Zkontroluj, zda už je v vlastní sekci
$custom_section_id = Tab::getIdFromClassName('AdminCustomModules');
if ($custom_section_id && $tab_info['id_parent'] == $custom_section_id) {
    echo "<p style='color: green;'>✅ Tab je už v sekci 'Vlastní moduly'</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Tab NENÍ v sekci 'Vlastní moduly'</p>";
    
    if (isset($_GET['action']) && $_GET['action'] == 'move') {
        echo "<h2>Přesun do vlastní sekce...</h2>";
        
        // Přesuň tab do vlastní sekce
        $result = CustomModulesTabManager::moveTabToCustomModules($controller_class);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Tab úspěšně přesunut do sekce 'Vlastní moduly'</p>";
            
            // Zkontroluj nový stav
            $new_tab_info = Db::getInstance()->getRow($sql);
            if ($new_tab_info) {
                echo "<p><strong>Nové umístění:</strong> Parent ID: {$new_tab_info['id_parent']}</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Chyba při přesunu tabu</p>";
        }
    } else {
        echo "<p><a href='?module=$module_name&controller=$controller_class&action=move' style='background: #4caf50; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>🔄 Přesunout do vlastní sekce</a></p>";
    }
}

echo "<h2>Úprava kódu modulu</h2>";

// Zkontroluj, zda modul už používá CustomModulesTabManager
$module_file = _PS_MODULE_DIR_ . $module_name . '/' . $module_name . '.php';
if (file_exists($module_file)) {
    $module_content = file_get_contents($module_file);
    
    if (strpos($module_content, 'CustomModulesTabManager') !== false) {
        echo "<p style='color: green;'>✅ Modul už používá CustomModulesTabManager</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Modul nepoužívá CustomModulesTabManager</p>";
        echo "<p><strong>Doporučení:</strong> Upravte metody createTab() a removeTab() v souboru $module_name.php:</p>";
        
        echo "<h3>Přidejte do createTab():</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>";
        echo "private function createTab()\n";
        echo "{\n";
        echo "    require_once(_PS_MODULE_DIR_ . \$this->name . '/classes/CustomModulesTabManager.php');\n";
        echo "    return CustomModulesTabManager::createModuleTab('$controller_class', 'Název modulu', \$this->name);\n";
        echo "}\n";
        echo "</pre>";
        
        echo "<h3>Přidejte do removeTab():</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>";
        echo "private function removeTab()\n";
        echo "{\n";
        echo "    require_once(_PS_MODULE_DIR_ . \$this->name . '/classes/CustomModulesTabManager.php');\n";
        echo "    return CustomModulesTabManager::removeModuleTab('$controller_class');\n";
        echo "}\n";
        echo "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ Hlavní soubor modulu nenalezen: $module_file</p>";
}

echo "<h2>Stav vlastní sekce</h2>";
if (CustomModulesTabManager::customModulesSectionExists()) {
    echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' existuje</p>";
    
    // Zobraz všechny moduly v sekci
    $custom_modules_id = Tab::getIdFromClassName('AdminCustomModules');
    $sql = 'SELECT t.class_name, tl.name 
            FROM `' . _DB_PREFIX_ . 'tab` t
            LEFT JOIN `' . _DB_PREFIX_ . 'tab_lang` tl ON (t.id_tab = tl.id_tab AND tl.id_lang = ' . (int)Context::getContext()->language->id . ')
            WHERE t.id_parent = ' . (int)$custom_modules_id . ' AND t.active = 1 
            ORDER BY t.position';
    
    $modules_in_section = Db::getInstance()->executeS($sql);
    if ($modules_in_section) {
        echo "<h3>Moduly v sekci:</h3>";
        echo "<ul>";
        foreach ($modules_in_section as $mod) {
            echo "<li><strong>{$mod['name']}</strong> ({$mod['class_name']})</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Sekce 'Vlastní moduly' neexistuje</p>";
    echo "<p><a href='?module=$module_name&controller=$controller_class&action=create_section' style='background: #2196f3; color: white; padding: 10px; text-decoration: none; border-radius: 4px;'>➕ Vytvořit sekci</a></p>";
    
    if (isset($_GET['action']) && $_GET['action'] == 'create_section') {
        echo "<h2>Vytváření sekce...</h2>";
        $result = CustomModulesTabManager::createOrGetCustomModulesTab();
        if ($result) {
            echo "<p style='color: green;'>✅ Sekce 'Vlastní moduly' vytvořena (ID: $result)</p>";
        } else {
            echo "<p style='color: red;'>❌ Chyba při vytváření sekce</p>";
        }
    }
}

echo "<hr>";
echo "<p><small>Script: " . __FILE__ . "</small></p>";
?>
