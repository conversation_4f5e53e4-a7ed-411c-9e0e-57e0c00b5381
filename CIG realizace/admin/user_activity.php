<?php
require_once '../includes/session.php';
require_once '../includes/auth_check.php';
require_once '../config/database.php';
require_once '../classes/HistoryLogger.php';

// Check admin access
if ($current_user['role'] !== 'admin') {
    header('Location: ../includes/access_denied.php');
    exit;
}

$page_title = 'Aktivita uživatelů';

// Get filters
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$user_id = $_GET['user_id'] ?? '';

try {
    $pdo = getDbConnection();
    
    // Get all users for filter
    $stmt = $pdo->query("SELECT id, username, full_name, role FROM users WHERE is_active = 1 ORDER BY full_name");
    $users = $stmt->fetchAll();
    
    // Get user activity statistics
    $sql = "
        SELECT u.id, u.username, u.full_name, u.role, u.created_at as user_created,
               COUNT(oh.id) as total_actions,
               COUNT(CASE WHEN oh.action_type = 'preview_status_change' THEN 1 END) as status_changes,
               COUNT(CASE WHEN oh.action_type = 'delivery_date_change' THEN 1 END) as date_changes,
               COUNT(CASE WHEN oh.action_type = 'technology_assignment' THEN 1 END) as tech_assignments,
               COUNT(CASE WHEN oh.action_type = 'item_relevance_change' THEN 1 END) as relevance_changes,
               COUNT(CASE WHEN oh.action_type = 'item_status_change' THEN 1 END) as status_updates,
               COUNT(DISTINCT oh.order_id) as orders_affected,
               MAX(oh.created_at) as last_activity,
               MIN(oh.created_at) as first_activity
        FROM users u
        LEFT JOIN order_history oh ON u.id = oh.user_id 
            AND DATE(oh.created_at) BETWEEN ? AND ?
        WHERE u.is_active = 1
    ";
    
    $params = [$date_from, $date_to];
    
    if ($user_id) {
        $sql .= " AND u.id = ?";
        $params[] = $user_id;
    }
    
    $sql .= " GROUP BY u.id ORDER BY total_actions DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $user_stats = $stmt->fetchAll();
    
    // Get daily activity for chart
    $daily_sql = "
        SELECT DATE(oh.created_at) as activity_date, 
               COUNT(*) as actions_count,
               COUNT(DISTINCT oh.user_id) as active_users
        FROM order_history oh
        WHERE DATE(oh.created_at) BETWEEN ? AND ?
    ";
    
    $daily_params = [$date_from, $date_to];
    
    if ($user_id) {
        $daily_sql .= " AND oh.user_id = ?";
        $daily_params[] = $user_id;
    }
    
    $daily_sql .= " GROUP BY DATE(oh.created_at) ORDER BY activity_date";
    
    $stmt = $pdo->prepare($daily_sql);
    $stmt->execute($daily_params);
    $daily_activity = $stmt->fetchAll();
    
    // Get top performers
    $top_sql = "
        SELECT u.username, u.full_name, u.role,
               COUNT(oh.id) as total_actions,
               COUNT(DISTINCT oh.order_id) as orders_affected
        FROM users u
        INNER JOIN order_history oh ON u.id = oh.user_id
        WHERE DATE(oh.created_at) BETWEEN ? AND ?
        GROUP BY u.id
        ORDER BY total_actions DESC
        LIMIT 10
    ";
    
    $stmt = $pdo->prepare($top_sql);
    $stmt->execute([$date_from, $date_to]);
    $top_performers = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $db_error = $e->getMessage();
}

// Helper functions
function formatTimeAgo($datetime) {
    if (!$datetime) return 'Nikdy';
    
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'před chvílí';
    if ($time < 3600) return 'před ' . floor($time/60) . ' min';
    if ($time < 86400) return 'před ' . floor($time/3600) . ' h';
    if ($time < 2592000) return 'před ' . floor($time/86400) . ' dny';
    
    return date('d.m.Y H:i', strtotime($datetime));
}

function getActivityLevel($actions) {
    if ($actions >= 100) return ['level' => 'Velmi vysoká', 'class' => 'success'];
    if ($actions >= 50) return ['level' => 'Vysoká', 'class' => 'info'];
    if ($actions >= 20) return ['level' => 'Střední', 'class' => 'warning'];
    if ($actions > 0) return ['level' => 'Nízká', 'class' => 'secondary'];
    return ['level' => 'Žádná', 'class' => 'light'];
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
    <link href="../assets/css/reports.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-users me-2"></i>CIG Realizace
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="../reports/index.php">
                    <i class="fas fa-chart-line me-1"></i>Reporty
                </a>
                <a class="nav-link" href="system_activity.php">
                    <i class="fas fa-server me-1"></i>Systémová aktivita
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>Odhlásit
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-users me-2"></i><?= $page_title ?></h1>
                    <div class="export-buttons">
                        <button type="button" class="export-btn pdf" onclick="exportReport('pdf')">
                            <i class="fas fa-file-pdf"></i>Export PDF
                        </button>
                        <button type="button" class="export-btn excel" onclick="exportReport('excel')">
                            <i class="fas fa-file-excel"></i>Export Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <?php if (isset($db_error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Chyba připojení k databázi: <?= htmlspecialchars($db_error) ?>
        </div>
        <?php else: ?>

        <!-- Filters -->
        <div class="report-filters">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="date_from" class="form-label">Od data</label>
                    <input type="date" class="form-control report-filter" id="date_from" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                </div>
                <div class="col-md-4">
                    <label for="date_to" class="form-label">Do data</label>
                    <input type="date" class="form-control report-filter" id="date_to" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                </div>
                <div class="col-md-4">
                    <label for="user_id" class="form-label">Uživatel</label>
                    <select class="form-select report-filter" id="user_id" name="user_id">
                        <option value="">Všichni uživatelé</option>
                        <?php foreach ($users as $user): ?>
                        <option value="<?= $user['id'] ?>" <?= $user_id == $user['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($user['full_name']) ?> (<?= htmlspecialchars($user['role']) ?>)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>Aplikovat filtry
                    </button>
                    <a href="user_activity.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Zrušit filtry
                    </a>
                </div>
            </form>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>Denní aktivita</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dailyActivityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-trophy me-2"></i>Top 10 uživatelů</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="topUsersChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Statistics Table -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>Detailní statistiky uživatelů</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="userStatsTable">
                        <thead>
                            <tr>
                                <th>Uživatel</th>
                                <th>Role</th>
                                <th>Celkem akcí</th>
                                <th>Změny stavů</th>
                                <th>Změny termínů</th>
                                <th>Technologie</th>
                                <th>Relevance</th>
                                <th>Objednávky</th>
                                <th>Aktivita</th>
                                <th>Poslední aktivita</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($user_stats as $user): ?>
                            <?php $activity = getActivityLevel($user['total_actions']); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <?= strtoupper(substr($user['full_name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?= htmlspecialchars($user['full_name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($user['username']) ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge <?= $user['role'] ?>">
                                        <?= htmlspecialchars($user['role']) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $activity['class'] ?> fs-6">
                                        <?= number_format($user['total_actions']) ?>
                                    </span>
                                </td>
                                <td><?= number_format($user['status_changes']) ?></td>
                                <td><?= number_format($user['date_changes']) ?></td>
                                <td><?= number_format($user['tech_assignments']) ?></td>
                                <td><?= number_format($user['relevance_changes']) ?></td>
                                <td><?= number_format($user['orders_affected']) ?></td>
                                <td>
                                    <span class="badge bg-<?= $activity['class'] ?>">
                                        <?= $activity['level'] ?>
                                    </span>
                                </td>
                                <td>
                                    <small><?= formatTimeAgo($user['last_activity']) ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/reports.js"></script>
    <script>
        // Initialize charts with data
        const dailyActivityData = <?= json_encode($daily_activity) ?>;
        const topPerformersData = <?= json_encode($top_performers) ?>;
        
        // Initialize reports page
        if (typeof Reports !== 'undefined') {
            Reports.init();
            
            // Initialize daily activity chart
            if (dailyActivityData.length > 0) {
                const ctx = document.getElementById('dailyActivityChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: dailyActivityData.map(item => {
                                const date = new Date(item.activity_date);
                                return date.toLocaleDateString('cs-CZ');
                            }),
                            datasets: [{
                                label: 'Počet akcí',
                                data: dailyActivityData.map(item => parseInt(item.actions_count)),
                                borderColor: '#667eea',
                                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                                borderWidth: 3,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            }
            
            // Initialize top users chart
            if (topPerformersData.length > 0) {
                const ctx = document.getElementById('topUsersChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: topPerformersData.map(item => item.full_name),
                            datasets: [{
                                label: 'Počet akcí',
                                data: topPerformersData.map(item => parseInt(item.total_actions)),
                                backgroundColor: [
                                    '#667eea', '#764ba2', '#f093fb', '#f5576c',
                                    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                                    '#fa709a', '#fee140'
                                ],
                                borderRadius: 8
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            }
        }
    </script>
</body>
</html>
