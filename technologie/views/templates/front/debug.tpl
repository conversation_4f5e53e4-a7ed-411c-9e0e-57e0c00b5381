{**
 * Debug šablona pro diagnostiku technologie modulu
 * 
 * <AUTHOR> <<EMAIL>>
 * @version 1.3.0
 * @since 2024-12-19
 *}

{extends file='page.tpl'}

{block name='page_title'}
    Debug - Technologie modul
{/block}

{block name='page_header_container'}{/block}

{block name='page_content'}
<div class="technologie-debug">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="debug-header">
                    <h1 class="debug-title">🔧 Debug - Technologie modul</h1>
                    <p class="debug-subtitle">Diagnostické informace pro řešení problémů s routing</p>
                </div>

                {* Aktuální URL informace *}
                <div class="debug-section">
                    <h2>📍 Aktuální URL informace</h2>
                    <div class="debug-info">
                        <p><strong>Aktu<PERSON>lní URL:</strong> <code>{$debug_info.current_url}</code></p>
                        <p><strong>Slug parametr:</strong> <code>{$debug_info.slug_param|default:'N/A'}</code></p>
                        <p><strong>Action parametr:</strong> <code>{$debug_info.action_param|default:'N/A'}</code></p>
                    </div>
                </div>

                {* Správné routes *}
                <div class="debug-section">
                    <h2>🛣️ Správné routes</h2>
                    <div class="debug-info">
                        <p><strong>Seznam technologií:</strong></p>
                        <ul>
                            <li>Pattern: <code>{$debug_info.module_routes.list_route.pattern}</code></li>
                            <li>URL: <a href="{$debug_info.module_routes.list_route.example}" target="_blank">{$debug_info.module_routes.list_route.example}</a></li>
                        </ul>
                        
                        <p><strong>Detail technologie:</strong></p>
                        <ul>
                            <li>Pattern: <code>{$debug_info.module_routes.detail_route.pattern}</code></li>
                            <li>Příklad: <a href="{$debug_info.module_routes.detail_route.example}" target="_blank">{$debug_info.module_routes.detail_route.example}</a></li>
                        </ul>
                    </div>
                </div>

                {* Dostupné technologie *}
                <div class="debug-section">
                    <h2>🔧 Dostupné technologie</h2>
                    <div class="debug-info">
                        {if $debug_info.available_technologies}
                            <table class="debug-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Název</th>
                                        <th>Slug</th>
                                        <th>Aktivní</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {foreach from=$debug_info.available_technologies item=tech}
                                        <tr>
                                            <td>{$tech.id}</td>
                                            <td>{$tech.name}</td>
                                            <td><code>{$tech.slug|default:'N/A'}</code></td>
                                            <td>{if $tech.active}✅ Ano{else}❌ Ne{/if}</td>
                                        </tr>
                                    {/foreach}
                                </tbody>
                            </table>
                        {else}
                            <p class="debug-warning">⚠️ Žádné technologie nebyly nalezeny v databázi!</p>
                        {/if}
                    </div>
                </div>

                {* Správné URL pro technologie *}
                <div class="debug-section">
                    <h2>🔗 Správné URL pro technologie</h2>
                    <div class="debug-info">
                        {if $debug_info.correct_urls}
                            <ul class="debug-urls">
                                {foreach from=$debug_info.correct_urls item=url_info}
                                    <li>
                                        <strong>{$url_info.name}:</strong><br>
                                        <a href="{$url_info.url}" target="_blank" class="debug-link">{$url_info.url}</a>
                                        <small>(slug: <code>{$url_info.slug}</code>)</small>
                                    </li>
                                {/foreach}
                            </ul>
                        {else}
                            <p class="debug-warning">⚠️ Žádné URL nebyly vygenerovány!</p>
                        {/if}
                    </div>
                </div>

                {* Návod k řešení *}
                <div class="debug-section">
                    <h2>💡 Návod k řešení</h2>
                    <div class="debug-info">
                        <div class="debug-solution">
                            <h3>Pokud vidíte chybu "Hledaná stránka nebyla nalezena":</h3>
                            <ol>
                                <li>Zkontrolujte, že používáte správnou URL strukturu: <code>/reklamni-potisk/slug</code></li>
                                <li>Místo <code>/modules/technologie/detail/sitotisk</code> použijte <code>/reklamni-potisk/sitotisk</code></li>
                                <li>Ověřte, že technologie existuje v databázi a je aktivní (viz tabulka výše)</li>
                                <li>Zkuste kliknout na odkazy výše pro test</li>
                            </ol>
                        </div>
                    </div>
                </div>

                {* Akce *}
                <div class="debug-actions">
                    <a href="{$debug_info.module_routes.list_route.example}" class="btn btn-primary">
                        📋 Přejít na seznam technologií
                    </a>
                    {if $debug_info.correct_urls && $debug_info.correct_urls[0]}
                        <a href="{$debug_info.correct_urls[0].url}" class="btn btn-secondary">
                            🔍 Test první technologie
                        </a>
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.technologie-debug {
    padding: 20px 0;
    font-family: 'Courier New', monospace;
}

.debug-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.debug-title {
    color: #333;
    margin-bottom: 10px;
}

.debug-subtitle {
    color: #666;
    margin: 0;
}

.debug-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #fff;
}

.debug-section h2 {
    color: #007bff;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.debug-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

.debug-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.debug-table th,
.debug-table td {
    padding: 8px 12px;
    border: 1px solid #ddd;
    text-align: left;
}

.debug-table th {
    background: #007bff;
    color: white;
}

.debug-table tr:nth-child(even) {
    background: #f2f2f2;
}

.debug-urls {
    list-style: none;
    padding: 0;
}

.debug-urls li {
    margin-bottom: 15px;
    padding: 10px;
    background: #e9ecef;
    border-radius: 5px;
}

.debug-link {
    color: #007bff;
    text-decoration: none;
    word-break: break-all;
}

.debug-link:hover {
    text-decoration: underline;
}

.debug-warning {
    color: #dc3545;
    font-weight: bold;
}

.debug-solution {
    background: #d4edda;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #28a745;
}

.debug-solution h3 {
    color: #155724;
    margin-bottom: 10px;
}

.debug-solution ol {
    margin: 0;
    padding-left: 20px;
}

.debug-actions {
    text-align: center;
    margin-top: 30px;
}

.debug-actions .btn {
    margin: 0 10px;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    display: inline-block;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
</style>
{/block}
