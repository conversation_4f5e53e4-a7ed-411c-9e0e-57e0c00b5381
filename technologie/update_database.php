<?php
/**
 * Pomocný skript pro aktualizaci databáze technologií
 * Spustí upgrade skript pro přidání slug a detailních informací
 */

// Kontrola, že je skript spuštěn z PrestaShop prostředí
if (!defined('_PS_VERSION_')) {
    require_once dirname(__FILE__) . '/../../config/config.inc.php';
}

// Načtení upgrade skriptu
$upgradeFile = dirname(__FILE__) . '/sql/upgrade.sql';

if (!file_exists($upgradeFile)) {
    die('Upgrade soubor nenalezen: ' . $upgradeFile);
}

$sql = file_get_contents($upgradeFile);

// Nahrazení PREFIX_ skutečným prefixem
$sql = str_replace('PREFIX_', _DB_PREFIX_, $sql);

// Rozdělení na jednotlivé dotazy
$queries = array_filter(array_map('trim', explode(';', $sql)));

echo "<h2>Aktualizace databáze technologií</h2>\n";
echo "<pre>\n";

$success = 0;
$errors = 0;

foreach ($queries as $query) {
    if (empty($query) || strpos($query, '--') === 0) {
        continue;
    }
    
    echo "Spouštím: " . substr($query, 0, 100) . "...\n";
    
    try {
        $result = Db::getInstance()->execute($query);
        if ($result) {
            echo "✅ Úspěch\n";
            $success++;
        } else {
            echo "❌ Chyba: " . Db::getInstance()->getMsgError() . "\n";
            $errors++;
        }
    } catch (Exception $e) {
        echo "❌ Výjimka: " . $e->getMessage() . "\n";
        $errors++;
    }
    
    echo "\n";
}

echo "</pre>\n";
echo "<h3>Výsledek:</h3>\n";
echo "<p>Úspěšných dotazů: <strong>$success</strong></p>\n";
echo "<p>Chybných dotazů: <strong>$errors</strong></p>\n";

if ($errors === 0) {
    echo "<p style='color: green;'>✅ Aktualizace proběhla úspěšně!</p>\n";
} else {
    echo "<p style='color: red;'>❌ Některé dotazy selhaly. Zkontrolujte chyby výše.</p>\n";
}

// Kontrola aktuálního stavu databáze
echo "<h3>Kontrola aktuálního stavu:</h3>\n";
echo "<pre>\n";

try {
    $result = Db::getInstance()->executeS('SELECT name, slug, active FROM `' . _DB_PREFIX_ . 'technologie` ORDER BY position ASC');
    
    if ($result) {
        echo "Technologie v databázi:\n";
        foreach ($result as $row) {
            $status = $row['active'] ? '✅' : '❌';
            $slug = $row['slug'] ?: '❌ CHYBÍ';
            echo sprintf("- %s %s (slug: %s)\n", $status, $row['name'], $slug);
        }
    } else {
        echo "❌ Nepodařilo se načíst technologie z databáze\n";
    }
} catch (Exception $e) {
    echo "❌ Chyba při kontrole: " . $e->getMessage() . "\n";
}

echo "</pre>\n";
?>
