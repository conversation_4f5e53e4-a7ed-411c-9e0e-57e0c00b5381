# Oprava duplicitního zobrazení obsahu modulu Katalogy

## Problém
Po přehrání modulu se obsah katalogů zobrazoval duplicitně na stránce https://czimg-dev1.www2.peterman.cz/content/23-katalogy-reklamnich-predmetu-ke-stazeni a možná i v levém sidebaru.

## Př<PERSON>čina
Modul měl registrovaných příliš mnoho hooků, které všechny zobrazovaly stejný obsah:

1. **hookDisplayCMSContent** - zobrazoval obsah v hlavní části CMS
2. **hookDisplayRightColumn** - zobrazoval obsah v pravém sloupci  
3. **hookDisplayLeftColumn** - zobrazoval obsah v levém sloupci
4. **hookDisplayTop** - zobrazoval obsah nahoře
5. **hookDisplayBeforeBodyClosingTag** - p<PERSON><PERSON><PERSON><PERSON> obsah přes JavaScript
6. **Override CmsController** - zpracovával shortcode přímo v PHP
7. **hookFilterCmsContent** - další zpracování shortcode

Všechny tyto mechanismy kontrolovaly `isCatalogPage()` a pokud byla stránka detekována jako katalogová, vrátily `renderKatalogyContent()`, což způsobilo vícenásobné zobrazení stejného obsahu.

## Provedené opravy

### 1. Deaktivace duplicitních hooků
```php
public function hookDisplayRightColumn($params)
{
    // Nezobrazeovat katalogy v pravém sloupci - může způsobit duplicitní obsah
    return '';
}

public function hookDisplayLeftColumn($params)
{
    // Nezobrazeovat katalogy v levém sloupci - může způsobit duplicitní obsah
    return '';
}

public function hookDisplayTop($params)
{
    // Nezobrazeovat katalogy nahoře - může způsobit duplicitní obsah
    return '';
}
```

### 2. Zlepšení hookDisplayCMSContent
Přidána lepší logika pro rozhodování, kdy zobrazit obsah:
- Pokud CMS obsahuje shortcode, nechá zpracování na JavaScript
- Automatická detekce funguje pouze pokud není nastaven konkrétní CMS ID
- Pokud je nastaven konkrétní CMS ID, zobrazí obsah pouze na té stránce

### 3. Optimalizace hookDisplayBeforeBodyClosingTag
- Spouští se pouze když CMS stránka obsahuje shortcode
- Negeneruje JavaScript pro automatickou detekci katalogových stránek

### 4. Optimalizace generateShortcodeScript()
- Generuje obsah pouze pro shortcode, které jsou skutečně na stránce
- Kontroluje, zda stránka obsahuje `[katalogy]` nebo `[katalogy-simple]`
- Nepřipravuje zbytečně oba typy obsahu

### 5. Odstranění duplicitních mechanismů
- **Odstraněn override CmsController** - způsoboval dvojité zpracování shortcode
- **Odstraněn hookFilterCmsContent** - další duplicitní zpracování

## Způsoby zobrazení po opravě

### 1. Automatické zobrazení
Modul automaticky detekuje CMS stránku s "katalog" v názvu nebo URL a zobrazí kompletní obsah.

### 2. Ruční vložení shortcode
Do obsahu CMS stránky vložte:
- `[katalogy]` - kompletní obsah s úvodním textem
- `[katalogy-simple]` - pouze katalogy bez úvodního textu

### 3. Ruční vložení hook
Do CMS stránky vložte:
- `{hook h='displayKatalogyContent'}` - kompletní obsah
- `{hook h='displayKatalogySimple'}` - pouze katalogy

## Test oprav
Spusťte test script: `modules/katalogy/test-fix.php`

## Kontrolní seznam
- [ ] Obsah se zobrazuje pouze jednou na hlavní stránce katalogů
- [ ] Shortcode `[katalogy]` funguje správně
- [ ] Shortcode `[katalogy-simple]` funguje správně  
- [ ] Obsah se nezobrazuje duplicitně v sidebaru
- [ ] Formulář pro zájem o katalog funguje
- [ ] CSS a JavaScript se načítají správně

## Poznámky
- Hooky pro sloupce zůstávají registrované, ale nevrací žádný obsah
- Automatická detekce stále funguje pro zpětnou kompatibilitu
- JavaScript řešení je flexibilnější než PHP override
