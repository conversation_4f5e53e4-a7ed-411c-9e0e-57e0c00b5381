<?php
/**
 * Kontrola databázové struktury
 */

require_once dirname(__FILE__) . '/../../config/config.inc.php';

echo "<h1>Kontrola databáze modulu PriceInquiry</h1>";

// Zkontrolujeme, jestli tabulka existuje
$sql = "SHOW TABLES LIKE '"._DB_PREFIX_."price_inquiry'";
$result = Db::getInstance()->executeS($sql);

if (empty($result)) {
    echo "<p style='color: red;'>❌ Tabulka "._DB_PREFIX_."price_inquiry neexistuje!</p>";
    echo "<p>Spusť reinstalaci modulu.</p>";
} else {
    echo "<p style='color: green;'>✅ Tabulka "._DB_PREFIX_."price_inquiry existuje</p>";
    
    // Zkontrolujeme strukturu
    $sql = "DESCRIBE "._DB_PREFIX_."price_inquiry";
    $columns = Db::getInstance()->executeS($sql);
    
    echo "<h2>Struktura tabulky:</h2>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Sloupec</th><th>Typ</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $hasCorrectId = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'id_price_inquiry') {
            $hasCorrectId = true;
        }
    }
    echo "</table>";
    
    if (!$hasCorrectId) {
        echo "<p style='color: red;'>❌ Sloupec 'id_price_inquiry' neexistuje!</p>";
        echo "<p>Tabulka má starý formát. Potřebuje aktualizaci.</p>";
        
        // Nabídneme opravu
        if (isset($_GET['fix']) && $_GET['fix'] === '1') {
            echo "<h2>Opravuji strukturu...</h2>";
            
            // Zkontrolujeme, jestli má starý sloupec
            $hasOldId = false;
            foreach ($columns as $column) {
                if ($column['Field'] === 'id_inquiry') {
                    $hasOldId = true;
                    break;
                }
            }
            
            if ($hasOldId) {
                $sql = "ALTER TABLE "._DB_PREFIX_."price_inquiry CHANGE `id_inquiry` `id_price_inquiry` int(10) unsigned NOT NULL AUTO_INCREMENT";
                if (Db::getInstance()->execute($sql)) {
                    echo "<p style='color: green;'>✅ Sloupec přejmenován z id_inquiry na id_price_inquiry</p>";
                } else {
                    echo "<p style='color: red;'>❌ Chyba při přejmenování sloupce</p>";
                }
            }
            
            // Přidáme chybějící sloupce
            $missingColumns = [
                'customer_company' => "ALTER TABLE "._DB_PREFIX_."price_inquiry ADD `customer_company` varchar(255) DEFAULT NULL",
                'quantity' => "ALTER TABLE "._DB_PREFIX_."price_inquiry ADD `quantity` int(10) unsigned DEFAULT 1"
            ];
            
            foreach ($missingColumns as $columnName => $sql) {
                $exists = false;
                foreach ($columns as $column) {
                    if ($column['Field'] === $columnName) {
                        $exists = true;
                        break;
                    }
                }
                
                if (!$exists) {
                    if (Db::getInstance()->execute($sql)) {
                        echo "<p style='color: green;'>✅ Přidán sloupec: $columnName</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Chyba při přidávání sloupce: $columnName</p>";
                    }
                }
            }
            
            echo "<p><a href='?'>Obnovit stránku</a></p>";
        } else {
            echo "<p><a href='?fix=1' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none;'>Opravit strukturu databáze</a></p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Struktura databáze je v pořádku</p>";
        
        // Zobrazíme data
        $sql = "SELECT * FROM "._DB_PREFIX_."price_inquiry ORDER BY date_add DESC LIMIT 5";
        $data = Db::getInstance()->executeS($sql);
        
        if (!empty($data)) {
            echo "<h2>Posledních 5 dotazů:</h2>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Zákazník</th><th>Produkt</th><th>Datum</th><th>Vyřešeno</th></tr>";
            
            foreach ($data as $row) {
                echo "<tr>";
                echo "<td>" . $row['id_price_inquiry'] . "</td>";
                echo "<td>" . htmlspecialchars($row['customer_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['product_name']) . "</td>";
                echo "<td>" . $row['date_add'] . "</td>";
                echo "<td>" . ($row['resolved'] ? 'Ano' : 'Ne') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>Žádné dotazy v databázi.</p>";
        }
    }
}
?>
