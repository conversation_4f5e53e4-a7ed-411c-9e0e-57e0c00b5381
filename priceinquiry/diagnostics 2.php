<?php
/**
 * Komplexní diagnostický soubor pro modul PriceInquiry
 */

// Nastavíme kódování
header('Content-Type: text/html; charset=UTF-8');

// Načteme PrestaShop
require_once dirname(__FILE__) . '/../../config/config.inc.php';

// CSS pro lepší zobrazení
echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; background: #f9f9f9; }
.ok { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; }
pre { background: #eee; padding: 10px; overflow-x: auto; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>';

echo "<h1>🔍 Diagnostika modulu PriceInquiry</h1>";

// 1. ZÁKLADNÍ INFORMACE O MODULU
echo '<div class="section">';
echo "<h2>1. Základní informace o modulu</h2>";

if (Module::isInstalled('priceinquiry')) {
    echo "<p class='ok'>✓ Modul je nainstalován</p>";
    
    if (Module::isEnabled('priceinquiry')) {
        echo "<p class='ok'>✓ Modul je aktivní</p>";
    } else {
        echo "<p class='error'>✗ Modul není aktivní</p>";
    }
    
    $module = Module::getInstanceByName('priceinquiry');
    if ($module) {
        echo "<p class='ok'>✓ Instance modulu načtena</p>";
        echo "<p><strong>Verze:</strong> " . $module->version . "</p>";
        echo "<p><strong>Název:</strong> " . $module->displayName . "</p>";
        echo "<p><strong>Název modulu:</strong> " . $module->name . "</p>";

        // Zkusíme získat cestu jinak
        $modulePath = _PS_MODULE_DIR_ . 'priceinquiry/';
        echo "<p><strong>Cesta modulu:</strong> " . $modulePath . "</p>";
        echo "<p><strong>Cesta existuje:</strong> " . (is_dir($modulePath) ? 'ANO' : 'NE') . "</p>";
    } else {
        echo "<p class='error'>✗ Nepodařilo se načíst instanci modulu</p>";
    }
} else {
    echo "<p class='error'>✗ Modul není nainstalován</p>";
}

$enabled = Configuration::get('PRICE_INQUIRY_ENABLED');
echo "<p><strong>PRICE_INQUIRY_ENABLED:</strong> " . ($enabled ? 'true' : 'false') . "</p>";
echo '</div>';

// 2. REGISTROVANÉ HOOKY
echo '<div class="section">';
echo "<h2>2. Registrované hooky</h2>";

if ($module) {
    $hooks = Hook::getHooks();
    $moduleHooks = [];
    foreach ($hooks as $hook) {
        try {
            if (Hook::isModuleRegisteredOnHook($module, $hook['name'], Shop::getContextShopID())) {
                $moduleHooks[] = $hook['name'];
            }
        } catch (Exception $e) {
            // Ignorujeme chyby
        }
    }
    
    if (!empty($moduleHooks)) {
        echo "<p class='ok'>✓ Registrované hooky:</p>";
        echo "<ul>";
        foreach ($moduleHooks as $hookName) {
            echo "<li>" . $hookName . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='error'>✗ Žádné registrované hooky</p>";
    }
}
echo '</div>';

// 3. TEST KONKRÉTNÍHO PRODUKTU
echo '<div class="section">';
echo "<h2>3. Test konkrétního produktu</h2>";

$productId = 9292; // ID z URL
$product = new Product($productId);

if (Validate::isLoadedObject($product)) {
    // Opravíme název produktu
    $productName = is_array($product->name) ? $product->name[1] : $product->name;
    echo "<p class='ok'>✓ Produkt načten: " . $productName . "</p>";

    // Nastavíme kontext pro správné fungování
    Context::getContext()->cart = new Cart();
    Context::getContext()->customer = new Customer();

    // Test různých variant
    $combinations = $product->getAttributeCombinations(1); // Použijeme ID jazyka 1
    
    echo "<h3>Varianty produktu:</h3>";
    echo "<table>";
    echo "<tr><th>ID Attribute</th><th>Kombinace</th><th>Cena</th><th>Měl by zobrazit 'Cena na dotaz'?</th></tr>";
    
    // Default varianta
    $defaultAttribute = Product::getDefaultAttribute($productId);
    try {
        $defaultPrice = Product::getPriceStatic($productId, true, $defaultAttribute);
    } catch (Exception $e) {
        $defaultPrice = "Chyba: " . $e->getMessage();
    }
    echo "<tr>";
    echo "<td>" . ($defaultAttribute ?: 'Default') . "</td>";
    echo "<td>Default</td>";
    echo "<td>" . $defaultPrice . " Kč</td>";
    echo "<td>" . (is_numeric($defaultPrice) && $defaultPrice <= 0 ? "<span class='ok'>ANO</span>" : "<span class='info'>NE</span>") . "</td>";
    echo "</tr>";
    
    // Ostatní varianty
    $processedAttributes = [];
    foreach ($combinations as $combination) {
        $attrId = $combination['id_product_attribute'];
        if (in_array($attrId, $processedAttributes)) continue;
        $processedAttributes[] = $attrId;
        
        $price = Product::getPriceStatic($productId, true, $attrId);
        $attrName = $combination['attribute_name'] . ': ' . $combination['group_name'];
        
        echo "<tr>";
        echo "<td>" . $attrId . "</td>";
        echo "<td>" . $attrName . "</td>";
        echo "<td>" . $price . " Kč</td>";
        echo "<td>" . ($price <= 0 ? "<span class='ok'>ANO</span>" : "<span class='info'>NE</span>") . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    echo "<p class='error'>✗ Nepodařilo se načíst produkt s ID " . $productId . "</p>";
}
echo '</div>';

// 4. SIMULACE HOOK VOLÁNÍ
echo '<div class="section">';
echo "<h2>4. Simulace hook volání</h2>";

if ($module) {
    echo "<h3>Test hookDisplayHeader:</h3>";

    // Nastavíme kontext pro test
    if (!Context::getContext()->controller) {
        Context::getContext()->controller = new stdClass();
        Context::getContext()->controller->php_self = 'product';
        Context::getContext()->controller->addCSS = function($path) { return "CSS: $path"; };
        Context::getContext()->controller->addJS = function($path) { return "JS: $path"; };
    }

    // Test hookDisplayHeader
    ob_start();
    try {
        $headerResult = $module->hookDisplayHeader();
    } catch (Exception $e) {
        $headerResult = "Chyba: " . $e->getMessage();
    }
    $headerOutput = ob_get_clean();

    if ($headerResult) {
        echo "<p class='ok'>✓ hookDisplayHeader vrátil obsah</p>";
        echo "<pre>" . htmlspecialchars($headerResult) . "</pre>";
    } else {
        echo "<p class='warning'>⚠ hookDisplayHeader nevrátil obsah (to je normální)</p>";
    }

    if ($headerOutput) {
        echo "<p class='info'>hookDisplayHeader debug výstup:</p>";
        echo "<pre>" . htmlspecialchars($headerOutput) . "</pre>";
    }

    if ($product) {
        echo "<h3>Test hookDisplayProductActions:</h3>";

        // Simulujeme různé varianty
        $testAttributes = [0, $defaultAttribute];
        if (!empty($combinations)) {
            $testAttributes[] = $combinations[0]['id_product_attribute'];
        }

        foreach (array_unique($testAttributes) as $attrId) {
            if ($attrId === null) continue;

            echo "<h4>Varianta ID: " . ($attrId ?: 'Default') . "</h4>";

            // Nastavíme $_GET pro simulaci
            $_GET['id_product_attribute'] = $attrId;

            // Zavoláme hook
            ob_start();
            $result = $module->hookDisplayProductActions([]);
            $output = ob_get_clean();

            if ($result) {
                echo "<p class='ok'>✓ Hook vrátil obsah</p>";
                echo "<pre>" . htmlspecialchars($result) . "</pre>";
            } else {
                echo "<p class='warning'>⚠ Hook nevrátil žádný obsah</p>";
            }

            if ($output) {
                echo "<p class='info'>Debug výstup:</p>";
                echo "<pre>" . htmlspecialchars($output) . "</pre>";
            }
        }
    }
}
echo '</div>';

// 5. KONTROLA SOUBORŮ
echo '<div class="section">';
echo "<h2>5. Kontrola souborů modulu</h2>";

$files = [
    'priceinquiry.php' => 'Hlavní soubor modulu',
    'views/js/front.js' => 'JavaScript soubor',
    'views/css/front.css' => 'CSS soubor',
    'views/templates/front/inquiry_block_detail.tpl' => 'Template pro detail',
    'views/templates/front/inquiry_modal.tpl' => 'Template pro modal',
    'config.xml' => 'Konfigurační soubor'
];

foreach ($files as $file => $description) {
    $fullPath = dirname(__FILE__) . '/' . $file;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        $modified = date('Y-m-d H:i:s', filemtime($fullPath));
        echo "<p class='ok'>✓ {$description}: {$file} ({$size} bytes, změněn: {$modified})</p>";

        // Speciální kontrola pro JavaScript
        if ($file === 'views/js/front.js') {
            $jsContent = file_get_contents($fullPath);
            if (strpos($jsContent, 'PriceInquiryJS') !== false) {
                echo "<p class='ok'>  ✓ JavaScript obsahuje PriceInquiryJS objekt</p>";
            } else {
                echo "<p class='error'>  ✗ JavaScript neobsahuje PriceInquiryJS objekt</p>";
            }
        }
    } else {
        echo "<p class='error'>✗ {$description}: {$file} - soubor neexistuje</p>";
    }
}

// Kontrola URL přístupnosti
$baseUrl = 'https://' . $_SERVER['HTTP_HOST'];
$jsUrl = $baseUrl . '/modules/priceinquiry/views/js/front.js';
$cssUrl = $baseUrl . '/modules/priceinquiry/views/css/front.css';

echo "<h3>Kontrola URL přístupnosti:</h3>";
echo "<p><a href='{$jsUrl}' target='_blank'>{$jsUrl}</a></p>";
echo "<p><a href='{$cssUrl}' target='_blank'>{$cssUrl}</a></p>";

echo '</div>';

// 6. JAVASCRIPT DIAGNOSTIKA
echo '<div class="section">';
echo "<h2>6. JavaScript diagnostika</h2>";
echo "<p>Otevřete konzoli prohlížeče (F12) a podívejte se na zprávy začínající 'PriceInquiry:'</p>";
echo "<script>
console.log('=== PriceInquiry Diagnostika ===');
console.log('JavaScript soubor načten:', typeof PriceInquiryJS !== 'undefined');
console.log('PrestaShop objekt:', typeof prestashop !== 'undefined');

// Test detekce cenových elementů
const priceSelectors = [
    '.product-prices .price',
    '.current-price .price', 
    '.product-price .price',
    '.price',
    '.current-price',
    '.product-prices'
];

console.log('=== Test cenových selektorů ===');
priceSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    console.log('Selector:', selector, 'Nalezeno:', elements.length);
    elements.forEach((el, index) => {
        console.log('  Element', index, ':', el.textContent.trim());
    });
});

// Test našeho bloku
const piBlocks = document.querySelectorAll('.price-inquiry-block-detail');
console.log('=== Naše bloky ===');
console.log('Nalezeno .price-inquiry-block-detail bloků:', piBlocks.length);
piBlocks.forEach((block, index) => {
    console.log('Blok', index, ':', {
        visible: window.getComputedStyle(block).display !== 'none',
        initialPrice: block.getAttribute('data-initial-price'),
        innerHTML: block.innerHTML.substring(0, 100) + '...'
    });
});
</script>";
echo '</div>';

echo "<h2>🎯 Shrnutí problémů</h2>";
echo "<p>Zkontrolujte konzoli prohlížeče pro JavaScript diagnostiku a error log serveru pro PHP zprávy.</p>";
echo "<p><strong>URL pro test:</strong> <a href='https://czimg-dev1.www2.peterman.cz/privesky/9292-19182-40-bluetooth-zarizeni-proti-ztrate-nebo-pro-nalezeni-klicu.html#/11-barva-cerna' target='_blank'>Test produkt</a></p>";
