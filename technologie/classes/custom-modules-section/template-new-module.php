<?php
/**
 * TEMPLATE pro nový custom modul s podporou vlastní sekce "VLASTNÍ MODULY"
 * Sekce se umístí hned po "KONFIGURACE", nad "ADVANCE SEO"
 * 
 * INSTRUKCE:
 * 1. Zkopírujte tento soubor a přejmenujte na nazev_modulu.php
 * 2. Nahraďte všechny výskyty "NAZEV_MODULU" za skutečný název (velkými písmeny)
 * 3. Nahraďte všechny výskyty "nazev_modulu" za skutečný název (malými písmeny)
 * 4. Zkopírujte soubor CustomModulesTabManager.php do složky classes/ vašeho modulu
 * 5. Vytvořte controller AdminNAZEV_MODULU.php v controllers/admin/
 * 6. Upravte podle potřeb vašeho modulu
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class NAZEV_MODULU extends Module
{
    public function __construct()
    {
        $this->name = 'nazev_modulu';
        $this->tab = 'administration';
        $this->version = '1.0.0';
        $this->author = 'Váš název';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '8.0.0',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Název modulu');
        $this->description = $this->l('Popis vašeho modulu');
        $this->confirmUninstall = $this->l('Opravdu chcete odinstalovat modul?');
    }

    public function install()
    {
        return parent::install() &&
            $this->registerHook('displayHeader') &&
            // Přidejte další hooky podle potřeby
            $this->createTables() &&
            $this->createTab() &&
            $this->createDefaultConfiguration();
    }

    public function uninstall()
    {
        return parent::uninstall() &&
            $this->dropTables() &&
            $this->removeTab() &&
            $this->deleteConfiguration();
    }

    /**
     * Vytvoření databázových tabulek
     */
    private function createTables()
    {
        // Příklad vytvoření tabulky
        $sql = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'nazev_modulu_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text,
            `active` tinyint(1) DEFAULT 1,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        return Db::getInstance()->execute($sql);
    }

    /**
     * Odstranění databázových tabulek
     */
    private function dropTables()
    {
        $sql = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'nazev_modulu_data`';
        return Db::getInstance()->execute($sql);
    }

    /**
     * Vytvoření tabu v administraci - POUŽIJE VLASTNÍ SEKCI "VLASTNÍ MODULY"
     */
    private function createTab()
    {
        // Načti univerzální třídu pro správu tabů
        require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
        
        // Vytvoř tab v sekci "Vlastní moduly"
        return CustomModulesTabManager::createModuleTab(
            'AdminNAZEV_MODULU',    // Název controller třídy (upravte podle vašeho modulu)
            'Název modulu',         // Zobrazovaný název v menu
            $this->name             // Název modulu
        );
    }

    /**
     * Odstranění tabu z administrace
     */
    private function removeTab()
    {
        // Načti univerzální třídu pro správu tabů
        require_once(_PS_MODULE_DIR_ . $this->name . '/classes/CustomModulesTabManager.php');
        
        // Odstraň tab a případně vyčisti prázdnou sekci
        return CustomModulesTabManager::removeModuleTab('AdminNAZEV_MODULU');
    }

    /**
     * Vytvoření výchozí konfigurace
     */
    private function createDefaultConfiguration()
    {
        Configuration::updateValue('NAZEV_MODULU_ENABLED', 1);
        Configuration::updateValue('NAZEV_MODULU_SETTING1', 'výchozí hodnota');
        // Přidejte další konfigurační hodnoty
        return true;
    }

    /**
     * Odstranění konfigurace
     */
    private function deleteConfiguration()
    {
        Configuration::deleteByName('NAZEV_MODULU_ENABLED');
        Configuration::deleteByName('NAZEV_MODULU_SETTING1');
        // Odstraňte další konfigurační hodnoty
        return true;
    }

    /**
     * Konfigurace modulu v administraci
     */
    public function getContent()
    {
        $output = '';
        
        if (Tools::isSubmit('submitNAZEV_MODULUConfig')) {
            $enabled = (int)Tools::getValue('NAZEV_MODULU_ENABLED');
            $setting1 = Tools::getValue('NAZEV_MODULU_SETTING1');
            
            Configuration::updateValue('NAZEV_MODULU_ENABLED', $enabled);
            Configuration::updateValue('NAZEV_MODULU_SETTING1', $setting1);
            
            $output .= $this->displayConfirmation($this->l('Nastavení bylo uloženo.'));
        }

        return $output . $this->displayForm();
    }

    /**
     * Formulář pro konfiguraci
     */
    public function displayForm()
    {
        $fields_form = [
            'form' => [
                'legend' => [
                    'title' => $this->l('Nastavení modulu'),
                    'icon' => 'icon-cogs'
                ],
                'input' => [
                    [
                        'type' => 'switch',
                        'label' => $this->l('Povolit modul'),
                        'name' => 'NAZEV_MODULU_ENABLED',
                        'is_bool' => true,
                        'values' => [
                            [
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Ano')
                            ],
                            [
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Ne')
                            ]
                        ]
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Nastavení 1'),
                        'name' => 'NAZEV_MODULU_SETTING1',
                        'desc' => $this->l('Popis nastavení')
                    ]
                ],
                'submit' => [
                    'title' => $this->l('Uložit'),
                    'class' => 'btn btn-default pull-right'
                ]
            ]
        ];

        $helper = new HelperForm();
        $helper->module = $this;
        $helper->name_controller = $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->currentIndex = AdminController::$currentIndex . '&configure=' . $this->name;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);
        $helper->title = $this->displayName;
        $helper->show_toolbar = true;
        $helper->toolbar_scroll = true;
        $helper->submit_action = 'submitNAZEV_MODULUConfig';

        $helper->fields_value['NAZEV_MODULU_ENABLED'] = Configuration::get('NAZEV_MODULU_ENABLED');
        $helper->fields_value['NAZEV_MODULU_SETTING1'] = Configuration::get('NAZEV_MODULU_SETTING1');

        return $helper->generateForm([$fields_form]);
    }

    /**
     * Příklad hook metody
     */
    public function hookDisplayHeader()
    {
        if (!Configuration::get('NAZEV_MODULU_ENABLED')) {
            return '';
        }
        
        // Přidej CSS/JS podle potřeby
        $this->context->controller->addCSS($this->_path . 'views/css/nazev_modulu.css');
        $this->context->controller->addJS($this->_path . 'views/js/nazev_modulu.js');
    }
}
